// src/backend/services/__tests__/playlist-export-service.test.ts

// Mock dependencies first
jest.mock('fs/promises');
jest.mock('../structured-logger-service');

import * as fs from 'fs/promises';
import { Playlist } from '../../models';
import { PlaylistRepository, SongRepository } from '../../repositories';
import {
  ExportFormat,
  ExportPlaylistRequest,
  PlaylistExportService,
} from '../playlist-export-service';
import { StructuredLoggerService } from '../structured-logger-service';

const mockPlaylistRepository = {
  findById: jest.fn(),
  findByName: jest.fn(),
  findAll: jest.fn(),
  findWithStats: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  search: jest.fn(),
  duplicate: jest.fn(),
} as unknown as PlaylistRepository;

const mockSongRepository = {
  findById: jest.fn(),
  findAll: jest.fn(),
  findByPlaylistId: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  search: jest.fn(),
  addToPlaylist: jest.fn(),
  removeFromPlaylist: jest.fn(),
  reorderInPlaylist: jest.fn(),
} as unknown as SongRepository;

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  logUserAction: jest.fn(),
} as unknown as StructuredLoggerService;

describe('PlaylistExportService', () => {
  let exportService: PlaylistExportService;
  let mockFs: typeof fs;

  beforeEach(() => {
    jest.clearAllMocks();
    mockFs = fs as any;

    exportService = new PlaylistExportService(
      mockPlaylistRepository,
      mockSongRepository,
      mockLogger,
    );
  });

  describe('exportPlaylists', () => {
    const mockPlaylist: Playlist = {
      id: 'playlist-1',
      name: 'Test Playlist',
      description: 'Test Description',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    };

    const mockSongs = [
      {
        id: 'song-1',
        title: 'Song 1',
        artist: 'Artist 1',
        album: 'Album 1',
        duration: 180,
        file_path: '/path/to/song1.mp3',
        position: 1,
        added_at: '2024-01-01T00:00:00Z',
      },
      {
        id: 'song-2',
        title: 'Song 2',
        artist: 'Artist 2',
        album: 'Album 2',
        duration: 240,
        file_path: '/path/to/song2.mp3',
        position: 2,
        added_at: '2024-01-01T00:00:00Z',
      },
    ];

    beforeEach(() => {
      (mockPlaylistRepository.findById as jest.Mock).mockResolvedValue(
        mockPlaylist,
      );
      (mockSongRepository.findByPlaylistId as jest.Mock).mockResolvedValue(
        mockSongs,
      );
      (mockFs.mkdir as jest.Mock).mockResolvedValue(undefined);
      (mockFs.writeFile as jest.Mock).mockResolvedValue(undefined);
      (mockFs.stat as jest.Mock).mockResolvedValue({ size: 1024 } as any);
    });

    it('should validate export request', async () => {
      const invalidRequest: ExportPlaylistRequest = {
        playlistIds: [],
        format: 'json' as ExportFormat,
        outputPath: '',
      };

      const result = await exportService.exportPlaylists(invalidRequest);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('VALIDATION_ERROR');
    });

    it('should export to JSON format successfully', async () => {
      const request: ExportPlaylistRequest = {
        playlistIds: ['playlist-1'],
        format: 'json',
        outputPath: '/test/export.json',
        includeMetadata: true,
      };

      const result = await exportService.exportPlaylists(request);

      expect(result.success).toBe(true);
      expect(result.format).toBe('json');
      expect(result.playlistCount).toBe(1);
      expect(result.songCount).toBe(2);
      expect(mockFs.writeFile).toHaveBeenCalledWith(
        '/test/export.json',
        expect.stringContaining('"format": "json"'),
        'utf8',
      );
    });

    it('should export to CSV format successfully', async () => {
      const request: ExportPlaylistRequest = {
        playlistIds: ['playlist-1'],
        format: 'csv',
        outputPath: '/test/export.csv',
      };

      const result = await exportService.exportPlaylists(request);

      expect(result.success).toBe(true);
      expect(result.format).toBe('csv');
      expect(mockFs.writeFile).toHaveBeenCalledWith(
        '/test/export.csv',
        expect.stringContaining('Playlist ID,Playlist Name'),
        'utf8',
      );
    });

    it('should export to M3U format successfully', async () => {
      const request: ExportPlaylistRequest = {
        playlistIds: ['playlist-1'],
        format: 'm3u',
        outputPath: '/test/export.m3u',
      };

      const result = await exportService.exportPlaylists(request);

      expect(result.success).toBe(true);
      expect(result.format).toBe('m3u');
      expect(mockFs.writeFile).toHaveBeenCalledWith(
        '/test/export.m3u',
        expect.stringContaining('#EXTM3U'),
        'utf8',
      );
    });

    it('should handle playlist not found error', async () => {
      (mockPlaylistRepository.findById as jest.Mock).mockResolvedValue(null);

      const request: ExportPlaylistRequest = {
        playlistIds: ['nonexistent-playlist'],
        format: 'json',
        outputPath: '/test/export.json',
      };

      const result = await exportService.exportPlaylists(request);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(2);
      expect(result.errors[0].type).toBe('PLAYLIST_NOT_FOUND');
      expect(result.errors[1].type).toBe('VALIDATION_ERROR');
      expect(result.errors[1].message).toContain('No valid playlists found');
    });

    it('should handle file write errors', async () => {
      (mockFs.writeFile as jest.Mock).mockRejectedValue(
        new Error('Write failed'),
      );

      const request: ExportPlaylistRequest = {
        playlistIds: ['playlist-1'],
        format: 'json',
        outputPath: '/test/export.json',
      };

      const result = await exportService.exportPlaylists(request);

      expect(result.success).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('FILE_WRITE_ERROR');
    });
  });

  describe('getSupportedFormats', () => {
    it('should return supported formats', () => {
      const formats = exportService.getSupportedFormats();
      expect(formats).toEqual(['json', 'csv', 'm3u']);
    });
  });

  describe('generateDefaultFilename', () => {
    it('should generate filename for single playlist', () => {
      const filename = exportService.generateDefaultFilename('json', 1);
      expect(filename).toMatch(
        /1-playlist-export-\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\.json/,
      );
    });

    it('should generate filename for multiple playlists', () => {
      const filename = exportService.generateDefaultFilename('csv', 3);
      expect(filename).toMatch(
        /3-playlists-export-\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\.csv/,
      );
    });

    it('should use custom timestamp', () => {
      const customDate = new Date('2024-01-01T12:00:00Z');
      const filename = exportService.generateDefaultFilename(
        'm3u',
        1,
        customDate,
      );
      expect(filename).toMatch(/1-playlist-export-2024-01-01-\d{2}-00-00\.m3u/);
    });
  });

  describe('validateOutputPath', () => {
    it('should validate existing directory', async () => {
      (mockFs.access as jest.Mock).mockResolvedValue(undefined);

      const result = await exportService.validateOutputPath(
        '/test/export.json',
        'json',
      );

      expect(result.isValid).toBe(true);
    });

    it('should suggest corrections for missing directory', async () => {
      (mockFs.access as jest.Mock).mockRejectedValue(
        new Error('Directory not found'),
      );

      const result = await exportService.validateOutputPath(
        '/nonexistent/export.json',
        'json',
      );

      expect(result.isValid).toBe(false);
      expect(result.suggestions).toContain('Create directory: /nonexistent');
    });

    it('should suggest adding file extension', async () => {
      (mockFs.access as jest.Mock).mockResolvedValue(undefined);

      const result = await exportService.validateOutputPath(
        '/test/export',
        'json',
      );

      expect(result.isValid).toBe(false);
      expect(result.correctedPath).toBe('/test/export.json');
    });
  });
});
