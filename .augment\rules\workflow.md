---
type: 'always_apply'
---

# CRITICAL WORKFLOW RULES

## RULE #1: TASK EVALUATION AND STATUS UPDATES (HIGHEST PRIORITY)

**MANDATORY WORKFLOW FOR EVERY INTERACTION:**

1. **ALWAYS START** by evaluating ALL tasks in `.taskmaster/tasks/` directory
2. **UPDATE STATUS** of completed work in task files using these markers:
   - ✅ = Completed
   - 🔄 = In Progress/Partially Completed
   - ❌ = Failed/Blocked
   - ⏳ = Pending

3. **CONTINUE WITH PENDING TASKS** unless explicitly instructed otherwise
4. **NEVER SKIP** task evaluation - this is the most important rule

## TASK STATUS UPDATE FORMAT:

```
# Task ID: X
# Title: Task Name
# Status: completed|in-progress|pending|blocked
# Dependencies: X, Y
# Priority: high|medium|low
# Description: Brief description
# Details:
1. ✅ Completed item description
2. 🔄 Partially completed item description
3. ⏳ Pending item description

# Subtasks:
## 1. Subtask Name [completed|in-progress|pending]
### Dependencies: None
### Description: What was implemented
### Details:
✅ Specific implementation details
🔄 What's partially done
⏳ What remains to be done

# Implementation Summary:
✅ What has been completed
🔄 What is in progress
⏳ What is pending

# Files Created/Modified:
- path/to/file.ts (NEW|MODIFIED|ENHANCED)
```

## WORKFLOW STEPS:

### Step 1: Task Evaluation (MANDATORY)

```bash
1. Read ALL task files in .taskmaster/tasks/
2. Identify current status of each task
3. Update task files with accurate completion status
4. Identify next pending task to work on
```

### Step 2: Implementation Priority

```bash
1. Continue with highest priority pending task
2. If no pending tasks, ask user for next direction
3. NEVER start new work without updating existing task status
```

### Step 3: Documentation

```bash
1. Update task files with implementation details
2. Document files created/modified
3. Note any dependencies or blockers
4. Provide clear status for each subtask
```

## TASK PRIORITY ORDER:

1. **High Priority**: Core functionality, user-facing features
2. **Medium Priority**: Enhancements, optimizations
3. **Low Priority**: Nice-to-have features, documentation

## CRITICAL REMINDERS:

- ⚠️ **NEVER** proceed without evaluating existing tasks
- ⚠️ **ALWAYS** update task status before new work
- ⚠️ **CONTINUE** with pending tasks unless told otherwise
- ⚠️ **ASK** user before switching to different task
- ⚠️ **DOCUMENT** all changes in task files

This workflow ensures:

- ✅ No work is lost or forgotten
- ✅ Clear progress tracking
- ✅ Systematic completion of all features
- ✅ Proper documentation of implementation
- ✅ Efficient use of development time
