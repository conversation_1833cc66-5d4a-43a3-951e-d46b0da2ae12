# Use the Clear Thought MCP for Systematic Problem Solving

**Whenever you encounter a complex problem, need to debug, or want to enhance your reasoning, leverage the Clear Thought MCP and its suite of tools.**

## Clear Thought MCP Capabilities

- **sequentialthinking**: Process thoughts step-by-step, with branching, revision, and memory management.
- **mentalmodel**: Apply mental models for systematic analysis.
- **debuggingapproach**: Use structured debugging methods to identify and resolve issues.
- **collaborativereasoning**: Facilitate reasoning with multiple perspectives or personas.
- **decisionframework**: Use structured frameworks for decision-making.
- **metacognitivemonitoring**: Monitor and assess your own thinking and knowledge.
- **socraticmethod**: Guide inquiry through systematic questioning.
- **creativethinking**: Employ creative and lateral thinking strategies.
- **systemsthinking**: Analyze complex systems and their interactions.
- **scientificmethod**: Apply the scientific method for systematic inquiry.
- **structuredargumentation**: Build and analyze structured arguments.
- **visualreasoning**: Use visual reasoning and diagrammatic operations.
- **session_info**: Retrieve current session statistics and activity.
- **session_export/session_import**: Export or import session state for backup or sharing.

## Guidance

- **Default to using Clear Thought MCP tools** for problem-solving, debugging, decision-making, and reasoning tasks.
- Select the tool that best fits your current challenge (e.g., use `debuggingapproach` for bugs, `decisionframework` for choices, `socraticmethod` for inquiry).
- Use `session_info`, `session_export`, and `session_import` to manage and share your session context as needed.
- Only use alternative approaches if Clear Thought MCP tools are not applicable.

This ensures a systematic, robust, and collaborative approach to problem-solving and reasoning throughout your workflow.
