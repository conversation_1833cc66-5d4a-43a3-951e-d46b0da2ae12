import { Grid, List, Plus } from 'lucide-react';
import React, { useState } from 'react';
import { usePlaylistList } from '../../hooks/usePlaylistQueries';
import { usePlaylistUIStore } from '../../stores/usePlaylistUIStore';
import { AddPlaylistDialog } from '../forms/AddPlaylistDialog';
import { Button } from '../ui/button';
import { PlaylistActionHandler } from './PlaylistActionHandler';
import { PlaylistActionsBar } from './PlaylistActionsBar';
import { PlaylistGrid, type Playlist } from './PlaylistGrid';

export interface MyPlaylistsProps {
  onPlaylistClick: (playlistId: string) => void;
}

export const MyPlaylists: React.FC<MyPlaylistsProps> = ({
  onPlaylistClick,
}) => {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const { viewMode, setViewMode, searchQuery, sortBy, filterBy } =
    usePlaylistUIStore();

  // Use React Query to fetch playlists
  const {
    data: playlists = [],
    isLoading,
    error,
    refetch,
  } = usePlaylistList({
    search: searchQuery,
    sortBy,
    // Add other filters as needed
  });

  const handleViewModeToggle = () => {
    setViewMode(viewMode === 'grid' ? 'list' : 'grid');
  };

  const handleAddPlaylist = () => {
    setShowAddDialog(true);
  };

  const handlePlaylistAdded = (_newPlaylist: Playlist) => {
    setShowAddDialog(false);
    // React Query will automatically update the cache
  };

  const handlePlaylistUpdated = (_updatedPlaylist: Playlist) => {
    // React Query will automatically update the cache
  };

  const handlePlaylistDeleted = (_playlistId: string) => {
    // React Query will automatically update the cache
  };

  const handlePlaylistDuplicated = (_newPlaylist: Playlist) => {
    // React Query will automatically update the cache
  };

  const renderEmptyState = () => (
    <div className='flex flex-col items-center justify-center py-12 text-center'>
      <div className='mb-4 rounded-full bg-muted p-6'>
        <Plus className='h-12 w-12 text-muted-foreground' />
      </div>
      <h3 className='mb-2 text-lg font-medium'>No playlists yet</h3>
      <p className='mb-6 max-w-md text-muted-foreground'>
        Start by creating a custom playlist or importing one from YouTube
      </p>
      <Button onClick={handleAddPlaylist}>
        <Plus className='mr-2 h-4 w-4' />
        Add Your First Playlist
      </Button>
    </div>
  );

  const renderError = () => (
    <div className='flex flex-col items-center justify-center py-12 text-center'>
      <div className='mb-4 rounded-full bg-destructive/10 p-6'>
        <span className='text-2xl'>⚠️</span>
      </div>
      <h3 className='mb-2 text-lg font-medium'>Failed to load playlists</h3>
      <p className='mb-6 max-w-md text-muted-foreground'>
        {error?.message ||
          'An unexpected error occurred while loading playlists'}
      </p>
      <Button variant='outline' onClick={() => refetch()}>
        Try Again
      </Button>
    </div>
  );

  const renderLoading = () => (
    <div className='flex flex-col items-center justify-center py-12 text-center'>
      <div className='mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent'></div>
      <p className='text-muted-foreground'>Loading playlists...</p>
    </div>
  );

  if (isLoading) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center justify-between'>
          <h1 className='text-2xl font-bold'>My Playlists</h1>
        </div>
        {renderLoading()}
      </div>
    );
  }

  if (error) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center justify-between'>
          <h1 className='text-2xl font-bold'>My Playlists</h1>
        </div>
        {renderError()}
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <h1 className='text-2xl font-bold'>My Playlists</h1>
        <div className='flex items-center gap-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={handleViewModeToggle}
            title={`Switch to ${viewMode === 'grid' ? 'list' : 'grid'} view`}
          >
            {viewMode === 'grid' ? (
              <List className='h-4 w-4' />
            ) : (
              <Grid className='h-4 w-4' />
            )}
          </Button>
          <Button onClick={handleAddPlaylist}>
            <Plus className='mr-2 h-4 w-4' />
            Add Playlist
          </Button>
        </div>
      </div>

      {/* Actions Bar */}
      <PlaylistActionsBar />

      {/* Playlist Content */}
      {playlists.length === 0 ? (
        renderEmptyState()
      ) : (
        <PlaylistActionHandler
          playlists={playlists}
          onPlaylistSelect={onPlaylistClick}
          onPlaylistUpdated={handlePlaylistUpdated}
          onPlaylistDeleted={handlePlaylistDeleted}
          onPlaylistDuplicated={handlePlaylistDuplicated}
        >
          {({ handlePlaylistAction, handleBulkAction }) => (
            <PlaylistGrid
              playlists={playlists}
              viewMode={viewMode}
              onPlaylistSelect={onPlaylistClick}
              onPlaylistAction={handlePlaylistAction}
              onAddPlaylist={handleAddPlaylist}
            />
          )}
        </PlaylistActionHandler>
      )}

      {/* Add Playlist Dialog */}
      <AddPlaylistDialog
        isOpen={showAddDialog}
        onClose={() => setShowAddDialog(false)}
        onPlaylistAdded={handlePlaylistAdded}
      />
    </div>
  );
};
