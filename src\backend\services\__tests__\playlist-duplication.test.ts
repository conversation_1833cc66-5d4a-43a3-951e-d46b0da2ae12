// src/backend/services/__tests__/playlist-duplication.test.ts

import {
  InvalidPlaylistOperationError,
  PlaylistNotFoundError,
} from '../../../shared/errors';
import { BackgroundTaskRepository } from '../../repositories/background-task-repository';
import { PlaylistRepository } from '../../repositories/playlist-repository';
import { SongRepository } from '../../repositories/song-repository';
import { SQLiteAdapter } from '../../sqlite-adapter';
import { BackgroundTaskService } from '../background-task-service';
import { PlaylistBusinessLogicService } from '../playlist-business-logic-service';
import { PlaylistCrudService } from '../playlist-crud-service';

// Mock dependencies
jest.mock('../../repositories/playlist-repository');
jest.mock('../../repositories/song-repository');
jest.mock('../../repositories/background-task-repository');
jest.mock('../../sqlite-adapter');
jest.mock('../playlist-crud-service');
jest.mock('../background-task-service');

describe('PlaylistBusinessLogicService - Duplication', () => {
  let businessLogicService: PlaylistBusinessLogicService;
  let mockCrudService: jest.Mocked<PlaylistCrudService>;
  let mockPlaylistRepository: jest.Mocked<PlaylistRepository>;
  let mockSongRepository: jest.Mocked<SongRepository>;
  let mockBackgroundTaskService: jest.Mocked<BackgroundTaskService>;

  const mockPlaylist = {
    id: 'playlist-1',
    name: 'Test Playlist',
    description: 'Test Description',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  };

  const mockPlaylistWithMetadata = {
    ...mockPlaylist,
    metadata: {
      tags: ['rock', 'classic'],
      isPrivate: false,
      lastModified: '2023-01-01T00:00:00Z',
      statistics: {
        totalDuration: 3600,
        songCount: 10,
        averageSongDuration: 360,
      },
    },
  };

  const mockSongs = [
    {
      id: 'song-1',
      title: 'Song 1',
      artist: 'Artist 1',
      album: 'Album 1',
      duration: 180,
      position: 1,
      added_at: '2023-01-01T00:00:00Z',
    },
    {
      id: 'song-2',
      title: 'Song 2',
      artist: 'Artist 2',
      album: 'Album 2',
      duration: 200,
      position: 2,
      added_at: '2023-01-01T00:00:00Z',
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock instances
    mockCrudService = new PlaylistCrudService(
      {} as PlaylistRepository,
      {} as SongRepository,
    ) as jest.Mocked<PlaylistCrudService>;

    mockPlaylistRepository = new PlaylistRepository(
      {} as SQLiteAdapter,
    ) as jest.Mocked<PlaylistRepository>;

    mockSongRepository = new SongRepository(
      {} as SQLiteAdapter,
    ) as jest.Mocked<SongRepository>;

    mockBackgroundTaskService = new BackgroundTaskService(
      {} as BackgroundTaskRepository,
    ) as jest.Mocked<BackgroundTaskService>;

    // Create service instance
    businessLogicService = new PlaylistBusinessLogicService(
      mockCrudService,
      mockPlaylistRepository,
      mockSongRepository,
    );

    // Setup default mocks
    mockCrudService.getPlaylistById.mockResolvedValue(mockPlaylist);
    mockCrudService.getPlaylistSongs.mockResolvedValue(mockSongs);
    mockPlaylistRepository.findByName.mockResolvedValue(null);
    mockCrudService.createPlaylist.mockResolvedValue(mockPlaylist);
    mockCrudService.addSongToPlaylist.mockResolvedValue(undefined);
  });

  describe('duplicatePlaylist', () => {
    it('should duplicate a playlist with unique name generation', async () => {
      // Mock getPlaylistWithMetadata to return the mock playlist
      jest
        .spyOn(businessLogicService, 'getPlaylistWithMetadata')
        .mockResolvedValue(mockPlaylistWithMetadata);

      jest
        .spyOn(businessLogicService, 'createPlaylistWithMetadata')
        .mockResolvedValue({
          ...mockPlaylistWithMetadata,
          id: 'playlist-2',
          name: 'Test Playlist (Copy)',
        });

      const result = await businessLogicService.duplicatePlaylist('playlist-1');

      expect(result.id).toBe('playlist-2');
      expect(result.name).toBe('Test Playlist (Copy)');
      expect(mockCrudService.getPlaylistSongs).toHaveBeenCalledWith(
        'playlist-1',
      );
      expect(mockCrudService.addSongToPlaylist).toHaveBeenCalledTimes(2);
    });

    it('should handle progress callback during duplication', async () => {
      const progressCallback = jest.fn();

      jest
        .spyOn(businessLogicService, 'getPlaylistWithMetadata')
        .mockResolvedValue(mockPlaylistWithMetadata);

      jest
        .spyOn(businessLogicService, 'createPlaylistWithMetadata')
        .mockResolvedValue({
          ...mockPlaylistWithMetadata,
          id: 'playlist-2',
          name: 'Test Playlist (Copy)',
        });

      await businessLogicService.duplicatePlaylist('playlist-1', undefined, {
        onProgress: progressCallback,
      });

      expect(progressCallback).toHaveBeenCalledWith(
        0,
        'Starting playlist duplication...',
      );
      expect(progressCallback).toHaveBeenCalledWith(
        0.1,
        'Loading source playlist...',
      );
      expect(progressCallback).toHaveBeenCalledWith(
        1.0,
        'Playlist duplication completed successfully',
      );
    });

    it('should handle duplication without songs when includeSongs is false', async () => {
      jest
        .spyOn(businessLogicService, 'getPlaylistWithMetadata')
        .mockResolvedValue(mockPlaylistWithMetadata);

      jest
        .spyOn(businessLogicService, 'createPlaylistWithMetadata')
        .mockResolvedValue({
          ...mockPlaylistWithMetadata,
          id: 'playlist-2',
          name: 'Test Playlist (Copy)',
        });

      await businessLogicService.duplicatePlaylist('playlist-1', undefined, {
        includeSongs: false,
      });

      expect(mockCrudService.getPlaylistSongs).not.toHaveBeenCalled();
      expect(mockCrudService.addSongToPlaylist).not.toHaveBeenCalled();
    });

    it('should handle errors during song copying gracefully', async () => {
      const progressCallback = jest.fn();

      jest
        .spyOn(businessLogicService, 'getPlaylistWithMetadata')
        .mockResolvedValue(mockPlaylistWithMetadata);

      jest
        .spyOn(businessLogicService, 'createPlaylistWithMetadata')
        .mockResolvedValue({
          ...mockPlaylistWithMetadata,
          id: 'playlist-2',
          name: 'Test Playlist (Copy)',
        });

      // Mock one song addition to fail
      mockCrudService.addSongToPlaylist
        .mockResolvedValueOnce(undefined)
        .mockRejectedValueOnce(new Error('Song copy failed'));

      const result = await businessLogicService.duplicatePlaylist(
        'playlist-1',
        undefined,
        {
          onProgress: progressCallback,
        },
      );

      expect(result.id).toBe('playlist-2');
      expect(progressCallback).toHaveBeenCalledWith(
        expect.any(Number),
        'Warning: Failed to copy song "Song 2"',
      );
    });

    it('should throw error when source playlist does not exist', async () => {
      mockCrudService.getPlaylistById.mockRejectedValue(
        new PlaylistNotFoundError('Playlist not found', 'invalid-id'),
      );

      await expect(
        businessLogicService.duplicatePlaylist('invalid-id'),
      ).rejects.toThrow(InvalidPlaylistOperationError);
    });
  });

  describe('duplicatePlaylistWithProgress', () => {
    it('should use synchronous duplication for small playlists', async () => {
      const smallSongs = [mockSongs[0]]; // Only 1 song
      mockCrudService.getPlaylistSongs.mockResolvedValue(smallSongs);

      jest
        .spyOn(businessLogicService, 'getPlaylistWithMetadata')
        .mockResolvedValue(mockPlaylistWithMetadata);

      jest.spyOn(businessLogicService, 'duplicatePlaylist').mockResolvedValue({
        ...mockPlaylistWithMetadata,
        id: 'playlist-2',
      });

      const result =
        await businessLogicService.duplicatePlaylistWithProgress('playlist-1');

      expect(result.playlistId).toBe('playlist-2');
      expect(result.taskId).toBeUndefined();
      expect(businessLogicService.duplicatePlaylist).toHaveBeenCalled();
    });

    it('should use background task for large playlists', async () => {
      // Create a large playlist (50+ songs)
      const largeSongs = Array.from({ length: 60 }, (_, i) => ({
        ...mockSongs[0],
        id: `song-${i}`,
        title: `Song ${i}`,
        position: i + 1,
      }));

      mockCrudService.getPlaylistSongs.mockResolvedValue(largeSongs);

      jest
        .spyOn(businessLogicService, 'getPlaylistWithMetadata')
        .mockResolvedValue({
          ...mockPlaylistWithMetadata,
          metadata: {
            ...mockPlaylistWithMetadata.metadata,
            statistics: {
              ...mockPlaylistWithMetadata.metadata.statistics,
              songCount: 60,
            },
          },
        });

      mockBackgroundTaskService.createTask.mockResolvedValue({
        id: 123,
        task_type: 'IMPORT_PLAYLIST',
        title: 'Duplicating playlist: Test Playlist (Copy)',
        status: 'QUEUED',
        progress: 0,
        target_id: 'playlist-1',
        created_at: new Date(),
        updated_at: new Date(),
        details: {
          operation: 'duplicate',
          sourceId: 'playlist-1',
          newName: 'Test Playlist (Copy)',
          includeSongs: true,
          includeMetadata: true,
          totalSongs: 60,
        },
      });

      const result = await businessLogicService.duplicatePlaylistWithProgress(
        'playlist-1',
        undefined,
        {
          backgroundTaskService: mockBackgroundTaskService,
        },
      );

      expect(result.taskId).toBe(123);
      expect(result.playlistId).toBe('');
      expect(mockBackgroundTaskService.createTask).toHaveBeenCalledWith({
        task_type: 'IMPORT_PLAYLIST',
        title: 'Duplicating playlist: Test Playlist (Copy)',
        target_id: 'playlist-1',
        details: {
          operation: 'duplicate',
          sourceId: 'playlist-1',
          newName: 'Test Playlist (Copy)',
          includeSongs: true,
          includeMetadata: true,
          totalSongs: 60,
        },
      });
    });

    it('should throw error when background task service is required but not provided', async () => {
      const largeSongs = Array.from({ length: 60 }, (_, i) => ({
        ...mockSongs[0],
        id: `song-${i}`,
        position: i + 1,
      }));

      mockCrudService.getPlaylistSongs.mockResolvedValue(largeSongs);

      jest
        .spyOn(businessLogicService, 'getPlaylistWithMetadata')
        .mockResolvedValue(mockPlaylistWithMetadata);

      await expect(
        businessLogicService.duplicatePlaylistWithProgress('playlist-1'),
      ).rejects.toThrow(InvalidPlaylistOperationError);
    });
  });

  describe('generateUniquePlaylistName', () => {
    it('should return original name if no conflict', async () => {
      mockPlaylistRepository.findByName.mockResolvedValue(null);

      const result =
        await businessLogicService.generateUniquePlaylistName('Unique Name');

      expect(result).toBe('Unique Name');
      expect(mockPlaylistRepository.findByName).toHaveBeenCalledWith(
        'Unique Name',
      );
    });

    it('should generate numbered suffix for conflicting names', async () => {
      mockPlaylistRepository.findByName
        .mockResolvedValueOnce(mockPlaylist) // 'Test Name' exists
        .mockResolvedValueOnce(mockPlaylist) // 'Test Name (2)' exists
        .mockResolvedValueOnce(null); // 'Test Name (3)' is available

      const result =
        await businessLogicService.generateUniquePlaylistName('Test Name');

      expect(result).toBe('Test Name (3)');
      expect(mockPlaylistRepository.findByName).toHaveBeenCalledTimes(3);
    });

    it('should throw error after too many attempts', async () => {
      mockPlaylistRepository.findByName.mockResolvedValue(mockPlaylist);

      await expect(
        businessLogicService.generateUniquePlaylistName('Always Taken'),
      ).rejects.toThrow(InvalidPlaylistOperationError);
    });
  });
});
