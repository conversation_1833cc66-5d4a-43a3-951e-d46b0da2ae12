// src/backend/services/playlist-export-service.ts

import * as fs from 'fs/promises';
import * as path from 'path';
import {
  PlaylistNotFoundError,
  SystemError,
  ValidationError,
} from '../../shared/errors';
import { ExportedPlaylist } from '../models';
import { PlaylistRepository, SongRepository } from '../repositories';
import { StructuredLoggerService } from './structured-logger-service';

// Export format types
export type ExportFormat = 'json' | 'csv' | 'm3u';

// Export request interfaces
export interface ExportPlaylistRequest {
  playlistIds: string[];
  format: ExportFormat;
  outputPath: string;
  includeMetadata?: boolean;
  includeSongFiles?: boolean;
  useRelativePaths?: boolean;
}

export interface ExportResult {
  success: boolean;
  filePath?: string;
  format: ExportFormat;
  playlistCount: number;
  songCount: number;
  errors: ExportError[];
  warnings: ExportWarning[];
  stats: ExportStats;
}

export interface ExportError {
  type:
    | 'PLAYLIST_NOT_FOUND'
    | 'FILE_WRITE_ERROR'
    | 'VALIDATION_ERROR'
    | 'UNKNOWN_ERROR';
  message: string;
  playlistId?: string;
  details?: any;
}

export interface ExportWarning {
  type: 'MISSING_FILE' | 'INVALID_PATH' | 'METADATA_INCOMPLETE';
  message: string;
  playlistId?: string;
  songId?: string;
  details?: any;
}

export interface ExportStats {
  totalPlaylists: number;
  totalSongs: number;
  totalDuration: number;
  exportedAt: string;
  exportDuration: number;
  fileSize?: number;
}

// JSON export structure
export interface JSONExportData {
  metadata: {
    exportedAt: string;
    format: 'json';
    version: '1.0';
    totalPlaylists: number;
    totalSongs: number;
    totalDuration: number;
  };
  playlists: ExportedPlaylist[];
}

// CSV export structure
export interface CSVExportRow {
  playlistId: string;
  playlistName: string;
  playlistDescription: string;
  songId: string;
  songTitle: string;
  songArtist: string;
  songAlbum: string;
  songDuration: number;
  songFilePath: string;
  songPosition: number;
  songAddedAt: string;
  playlistCreatedAt: string;
  playlistUpdatedAt: string;
}

export class PlaylistExportService {
  private logger: StructuredLoggerService;

  constructor(
    private playlistRepository: PlaylistRepository,
    private songRepository: SongRepository,
    logger?: StructuredLoggerService,
  ) {
    this.logger = logger || new StructuredLoggerService();
  }

  /**
   * Export playlists in the specified format
   */
  async exportPlaylists(request: ExportPlaylistRequest): Promise<ExportResult> {
    const startTime = Date.now();
    const errors: ExportError[] = [];
    const warnings: ExportWarning[] = [];

    try {
      // Validate request
      this.validateExportRequest(request);

      // Fetch playlist data
      const playlistsData = await this.fetchPlaylistsData(
        request.playlistIds,
        request.includeMetadata || false,
        errors,
        warnings,
      );

      if (playlistsData.length === 0) {
        throw new ValidationError('No valid playlists found to export');
      }

      // Calculate stats
      const stats = this.calculateExportStats(playlistsData, startTime);

      // Export based on format
      let filePath: string;
      switch (request.format) {
        case 'json':
          filePath = await this.exportToJSON(
            playlistsData,
            request.outputPath,
            stats,
          );
          break;
        case 'csv':
          filePath = await this.exportToCSV(
            playlistsData,
            request.outputPath,
            request.useRelativePaths || false,
          );
          break;
        case 'm3u':
          filePath = await this.exportToM3U(
            playlistsData,
            request.outputPath,
            request.useRelativePaths || false,
          );
          break;
        default:
          throw new ValidationError(
            `Unsupported export format: ${request.format}`,
          );
      }

      // Get file size
      const fileStats = await fs.stat(filePath);
      stats.fileSize = fileStats.size;

      this.logger.info('Playlist export completed successfully', {
        format: request.format,
        playlistCount: playlistsData.length,
        filePath,
        duration: Date.now() - startTime,
      });

      return {
        success: true,
        filePath,
        format: request.format,
        playlistCount: playlistsData.length,
        songCount: stats.totalSongs,
        errors,
        warnings,
        stats,
      };
    } catch (error) {
      this.logger.error('Playlist export failed', error as Error, {
        format: request.format,
        playlistIds: request.playlistIds,
        outputPath: request.outputPath,
      });

      // Add error to collection
      if (error instanceof PlaylistNotFoundError) {
        errors.push({
          type: 'PLAYLIST_NOT_FOUND',
          message: error.message,
          playlistId: error.details?.playlistId,
        });
      } else if (error instanceof ValidationError) {
        errors.push({
          type: 'VALIDATION_ERROR',
          message: error.message,
          details: error.details,
        });
      } else if (error instanceof SystemError) {
        errors.push({
          type: 'FILE_WRITE_ERROR',
          message: error.message,
          details: error.details,
        });
      } else {
        errors.push({
          type: 'UNKNOWN_ERROR',
          message: error instanceof Error ? error.message : String(error),
        });
      }

      return {
        success: false,
        format: request.format,
        playlistCount: 0,
        songCount: 0,
        errors,
        warnings,
        stats: {
          totalPlaylists: 0,
          totalSongs: 0,
          totalDuration: 0,
          exportedAt: new Date().toISOString(),
          exportDuration: Date.now() - startTime,
        },
      };
    }
  }

  /**
   * Export single playlist (convenience method)
   */
  async exportSinglePlaylist(
    playlistId: string,
    format: ExportFormat,
    outputPath: string,
    options?: {
      includeMetadata?: boolean;
      includeSongFiles?: boolean;
      useRelativePaths?: boolean;
    },
  ): Promise<ExportResult> {
    return this.exportPlaylists({
      playlistIds: [playlistId],
      format,
      outputPath,
      includeMetadata: options?.includeMetadata,
      includeSongFiles: options?.includeSongFiles,
      useRelativePaths: options?.useRelativePaths,
    });
  }

  /**
   * Get supported export formats
   */
  getSupportedFormats(): ExportFormat[] {
    return ['json', 'csv', 'm3u'];
  }

  /**
   * Validate export request
   */
  private validateExportRequest(request: ExportPlaylistRequest): void {
    if (!request.playlistIds || request.playlistIds.length === 0) {
      throw new ValidationError('At least one playlist ID is required');
    }

    if (!request.format) {
      throw new ValidationError('Export format is required');
    }

    if (!this.getSupportedFormats().includes(request.format)) {
      throw new ValidationError(`Unsupported export format: ${request.format}`);
    }

    if (!request.outputPath) {
      throw new ValidationError('Output path is required');
    }

    // Validate playlist IDs format
    for (const id of request.playlistIds) {
      if (!id || typeof id !== 'string' || id.trim().length === 0) {
        throw new ValidationError(`Invalid playlist ID: ${id}`);
      }
    }
  }

  /**
   * Fetch playlist data for export
   */
  private async fetchPlaylistsData(
    playlistIds: string[],
    includeMetadata: boolean,
    errors: ExportError[],
    warnings: ExportWarning[],
  ): Promise<ExportedPlaylist[]> {
    const playlistsData: ExportedPlaylist[] = [];

    for (const playlistId of playlistIds) {
      try {
        // Get playlist
        const playlist = await this.playlistRepository.findById(playlistId);
        if (!playlist) {
          errors.push({
            type: 'PLAYLIST_NOT_FOUND',
            message: `Playlist with ID "${playlistId}" not found`,
            playlistId,
          });
          continue;
        }

        // Get playlist songs
        const songs = await this.songRepository.findByPlaylistId(playlistId);

        // Build exported playlist data
        const exportedPlaylist: ExportedPlaylist = {
          playlist,
          songs: songs.map(song => ({
            id: song.id,
            title: song.title,
            artist: song.artist,
            album: song.album,
            duration: song.duration,
            file_path: song.file_path,
            position: song.position,
            added_at: song.added_at,
          })),
          metadata: {
            exportedAt: new Date().toISOString(),
            totalSongs: songs.length,
            totalDuration: songs.reduce(
              (sum, song) => sum + (song.duration || 0),
              0,
            ),
            format: 'json', // Will be updated based on actual format
          },
        };

        // Check for missing files if file paths are included
        if (includeMetadata) {
          for (const song of songs) {
            if (song.file_path) {
              try {
                await fs.access(song.file_path);
              } catch {
                warnings.push({
                  type: 'MISSING_FILE',
                  message: `Song file not found: ${song.file_path}`,
                  playlistId,
                  songId: song.id,
                  details: { filePath: song.file_path },
                });
              }
            }
          }
        }

        playlistsData.push(exportedPlaylist);
      } catch (error) {
        errors.push({
          type: 'UNKNOWN_ERROR',
          message: `Failed to fetch playlist data: ${error instanceof Error ? error.message : String(error)}`,
          playlistId,
        });
      }
    }

    return playlistsData;
  }

  /**
   * Calculate export statistics
   */
  private calculateExportStats(
    playlistsData: ExportedPlaylist[],
    startTime: number,
  ): ExportStats {
    const totalSongs = playlistsData.reduce(
      (sum, playlist) => sum + playlist.songs.length,
      0,
    );
    const totalDuration = playlistsData.reduce(
      (sum, playlist) => sum + playlist.metadata.totalDuration,
      0,
    );

    return {
      totalPlaylists: playlistsData.length,
      totalSongs,
      totalDuration,
      exportedAt: new Date().toISOString(),
      exportDuration: Date.now() - startTime,
    };
  }

  /**
   * Export to JSON format
   */
  private async exportToJSON(
    playlistsData: ExportedPlaylist[],
    outputPath: string,
    stats: ExportStats,
  ): Promise<string> {
    try {
      // Update format in metadata
      playlistsData.forEach(playlist => {
        playlist.metadata.format = 'json';
      });

      const jsonData: JSONExportData = {
        metadata: {
          exportedAt: stats.exportedAt,
          format: 'json',
          version: '1.0',
          totalPlaylists: stats.totalPlaylists,
          totalSongs: stats.totalSongs,
          totalDuration: stats.totalDuration,
        },
        playlists: playlistsData,
      };

      // Ensure output directory exists
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write JSON file
      const jsonString = JSON.stringify(jsonData, null, 2);
      await fs.writeFile(outputPath, jsonString, 'utf8');

      return outputPath;
    } catch (error) {
      throw new SystemError(
        `Failed to write JSON export file: ${error instanceof Error ? error.message : String(error)}`,
        'FILE_WRITE_ERROR',
        { details: { outputPath } },
      );
    }
  }

  /**
   * Export to CSV format
   */
  private async exportToCSV(
    playlistsData: ExportedPlaylist[],
    outputPath: string,
    useRelativePaths: boolean,
  ): Promise<string> {
    try {
      // Build CSV rows
      const csvRows: CSVExportRow[] = [];

      for (const playlistData of playlistsData) {
        const playlist = playlistData.playlist;

        for (const song of playlistData.songs) {
          let filePath = song.file_path || '';

          // Convert to relative path if requested
          if (useRelativePaths && filePath) {
            const outputDir = path.dirname(outputPath);
            filePath = path.relative(outputDir, filePath);
          }

          csvRows.push({
            playlistId: playlist.id,
            playlistName: playlist.name,
            playlistDescription: playlist.description || '',
            songId: song.id,
            songTitle: song.title,
            songArtist: song.artist,
            songAlbum: song.album || '',
            songDuration: song.duration || 0,
            songFilePath: filePath,
            songPosition: song.position,
            songAddedAt: song.added_at,
            playlistCreatedAt: playlist.created_at,
            playlistUpdatedAt: playlist.updated_at,
          });
        }
      }

      // Build CSV content
      const headers = [
        'Playlist ID',
        'Playlist Name',
        'Playlist Description',
        'Song ID',
        'Song Title',
        'Song Artist',
        'Song Album',
        'Song Duration (seconds)',
        'Song File Path',
        'Song Position',
        'Song Added At',
        'Playlist Created At',
        'Playlist Updated At',
      ];

      const csvContent = [
        headers.join(','),
        ...csvRows.map(row =>
          [
            this.escapeCsvValue(row.playlistId),
            this.escapeCsvValue(row.playlistName),
            this.escapeCsvValue(row.playlistDescription),
            this.escapeCsvValue(row.songId),
            this.escapeCsvValue(row.songTitle),
            this.escapeCsvValue(row.songArtist),
            this.escapeCsvValue(row.songAlbum),
            row.songDuration.toString(),
            this.escapeCsvValue(row.songFilePath),
            row.songPosition.toString(),
            this.escapeCsvValue(row.songAddedAt),
            this.escapeCsvValue(row.playlistCreatedAt),
            this.escapeCsvValue(row.playlistUpdatedAt),
          ].join(','),
        ),
      ].join('\n');

      // Ensure output directory exists
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write CSV file
      await fs.writeFile(outputPath, csvContent, 'utf8');

      return outputPath;
    } catch (error) {
      throw new SystemError(
        `Failed to write CSV export file: ${error instanceof Error ? error.message : String(error)}`,
        'FILE_WRITE_ERROR',
        { details: { outputPath } },
      );
    }
  }

  /**
   * Export to M3U format
   */
  private async exportToM3U(
    playlistsData: ExportedPlaylist[],
    outputPath: string,
    useRelativePaths: boolean,
  ): Promise<string> {
    try {
      const m3uLines: string[] = ['#EXTM3U'];

      for (const playlistData of playlistsData) {
        const playlist = playlistData.playlist;

        // Add playlist comment
        m3uLines.push(`# Playlist: ${playlist.name}`);
        if (playlist.description) {
          m3uLines.push(`# Description: ${playlist.description}`);
        }
        m3uLines.push(`# Created: ${playlist.created_at}`);
        m3uLines.push(`# Songs: ${playlistData.songs.length}`);
        m3uLines.push('');

        // Add songs
        for (const song of playlistData.songs) {
          if (song.file_path) {
            let filePath = song.file_path;

            // Convert to relative path if requested
            if (useRelativePaths) {
              const outputDir = path.dirname(outputPath);
              filePath = path.relative(outputDir, filePath);
            }

            // Add EXTINF line with duration and title
            const duration = song.duration ? Math.round(song.duration) : -1;
            const artist = song.artist ? `${song.artist} - ` : '';
            m3uLines.push(`#EXTINF:${duration},${artist}${song.title}`);
            m3uLines.push(filePath);
          }
        }

        // Add separator between playlists
        if (playlistsData.indexOf(playlistData) < playlistsData.length - 1) {
          m3uLines.push('');
          m3uLines.push('# ----------------------------------------');
          m3uLines.push('');
        }
      }

      const m3uContent = m3uLines.join('\n');

      // Ensure output directory exists
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write M3U file
      await fs.writeFile(outputPath, m3uContent, 'utf8');

      return outputPath;
    } catch (error) {
      throw new SystemError(
        `Failed to write M3U export file: ${error instanceof Error ? error.message : String(error)}`,
        'FILE_WRITE_ERROR',
        { details: { outputPath } },
      );
    }
  }

  /**
   * Escape CSV values to handle commas, quotes, and newlines
   */
  private escapeCsvValue(value: string): string {
    if (!value) {
return '';
}

    // If value contains comma, quote, or newline, wrap in quotes and escape internal quotes
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }

    return value;
  }

  /**
   * Generate default filename for export
   */
  generateDefaultFilename(
    format: ExportFormat,
    playlistCount: number,
    timestamp?: Date,
  ): string {
    const date = timestamp || new Date();
    const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD
    const timeStr = date.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS

    const playlistText = playlistCount === 1 ? 'playlist' : 'playlists';
    const baseName = `${playlistCount}-${playlistText}-export-${dateStr}-${timeStr}`;

    return `${baseName}.${format}`;
  }

  /**
   * Validate output path and suggest corrections
   */
  async validateOutputPath(
    outputPath: string,
    format: ExportFormat,
  ): Promise<{
    isValid: boolean;
    suggestions?: string[];
    correctedPath?: string;
  }> {
    try {
      // Check if directory exists
      const outputDir = path.dirname(outputPath);

      try {
        await fs.access(outputDir);
      } catch {
        return {
          isValid: false,
          suggestions: [
            `Create directory: ${outputDir}`,
            'Choose an existing directory',
            'Use a different output path',
          ],
          correctedPath: path.join(process.cwd(), path.basename(outputPath)),
        };
      }

      // Check file extension
      const expectedExt = `.${format}`;
      if (!outputPath.toLowerCase().endsWith(expectedExt)) {
        const correctedPath = outputPath + expectedExt;
        return {
          isValid: false,
          suggestions: [
            `Add ${expectedExt} extension`,
            'Choose a different format',
          ],
          correctedPath,
        };
      }

      // Check if file already exists
      try {
        await fs.access(outputPath);
        return {
          isValid: true,
          suggestions: [
            'File already exists and will be overwritten',
            'Choose a different filename',
          ],
        };
      } catch {
        // File doesn't exist, which is good
        return { isValid: true };
      }
    } catch (error) {
      return {
        isValid: false,
        suggestions: ['Check path permissions', 'Use a different output path'],
      };
    }
  }
}
