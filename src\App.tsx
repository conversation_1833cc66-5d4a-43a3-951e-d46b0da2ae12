import { StagewiseToolbar } from '@stagewise/toolbar-react';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { RouterProvider } from '@tanstack/react-router';
import React from 'react';
import { ErrorBoundary } from './frontend/components/common/ErrorBoundary';
import { ErrorNotificationManager } from './frontend/components/common/ErrorNotification';
import { queryClient } from './frontend/lib/query-client';
import { router } from './frontend/lib/router';
import './styles/globals.css';

export const App: React.FC = () => {
  console.log('🔍 DEBUGGING: App component rendering...');

  // Add a fallback in case router fails
  const [routerError, setRouterError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    console.log('🔍 DEBUGGING: App component mounted');

    // Check if router is properly initialized
    try {
      console.log('🔍 DEBUGGING: Router state:', router.state);
    } catch (error) {
      console.error('🔍 DEBUGGING: Router error:', error);
      setRouterError(error as Error);
    }
  }, []);

  if (routerError) {
    return (
      <div style={{ padding: '20px', color: 'red', fontFamily: 'Arial' }}>
        <h2>Router Error</h2>
        <p>Failed to initialize router: {routerError.message}</p>
        <button onClick={() => window.location.reload()}>Reload Page</button>
      </div>
    );
  }

  return (
    <ErrorBoundary
      maxRetries={3}
      autoRecover={false}
      onError={(error, errorInfo) => {
        console.error(
          '🔍 DEBUGGING: App Error Boundary caught error:',
          error,
          errorInfo,
        );
      }}
    >
      <QueryClientProvider client={queryClient}>
        <RouterProvider router={router} />
        <ErrorNotificationManager />
        {process.env.NODE_ENV === 'development' && (
          <>
            <ReactQueryDevtools initialIsOpen={false} />
            <StagewiseToolbar
              config={{
                plugins: [
                  // React plugin will be auto-detected from @stagewise-plugins/react package
                ],
              }}
            />
          </>
        )}
      </QueryClientProvider>
    </ErrorBoundary>
  );
};
