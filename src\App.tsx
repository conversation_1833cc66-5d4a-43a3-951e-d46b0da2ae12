import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { RouterProvider } from '@tanstack/react-router';
import React from 'react';
import { ErrorBoundary } from './frontend/components/common/ErrorBoundary';
import { ErrorNotificationManager } from './frontend/components/common/ErrorNotification';
import { queryClient } from './frontend/lib/query-client';
import { router } from './frontend/lib/router';
import './styles/globals.css';

export const App: React.FC = () => {
  console.log('🔍 DEBUGGING: App component rendering...');

  // Add a fallback in case router fails
  const [routerError, setRouterError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    console.log('🔍 DEBUGGING: App component mounted');

    // Check if router is properly initialized
    try {
      console.log('🔍 DEBUGGING: Router state:', router.state);
    } catch (error) {
      console.error('🔍 DEBUGGING: Router error:', error);
      setRouterError(error as Error);
    }

    // Initialize stagewise toolbar
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 STAGEWISE: Initializing toolbar...');
      import('@stagewise/toolbar')
        .then(({ initToolbar }) => {
          console.log('🔍 STAGEWISE: initToolbar imported successfully');
          initToolbar({
            plugins: [],
          });
          console.log('🔍 STAGEWISE: Toolbar initialized');
        })
        .catch(error => {
          console.error('🔍 STAGEWISE: Failed to initialize toolbar:', error);
        });
    }
  }, []);

  if (routerError) {
    return (
      <div style={{ padding: '20px', color: 'red', fontFamily: 'Arial' }}>
        <h2>Router Error</h2>
        <p>Failed to initialize router: {routerError.message}</p>
        <button onClick={() => window.location.reload()}>Reload Page</button>
      </div>
    );
  }

  return (
    <ErrorBoundary
      maxRetries={3}
      autoRecover={false}
      onError={(error, errorInfo) => {
        console.error(
          '🔍 DEBUGGING: App Error Boundary caught error:',
          error,
          errorInfo,
        );
      }}
    >
      <QueryClientProvider client={queryClient}>
        <RouterProvider router={router} />
        <ErrorNotificationManager />
        {process.env.NODE_ENV === 'development' && (
          <>
            <ReactQueryDevtools initialIsOpen={false} />
            {/* Temporary debug element to see if this renders */}
            <div
              style={{
                position: 'fixed',
                bottom: '20px',
                right: '20px',
                background: 'blue',
                color: 'white',
                padding: '10px',
                borderRadius: '5px',
                zIndex: 10000,
              }}
            >
              Stagewise should be here (NODE_ENV: {process.env.NODE_ENV})
            </div>
          </>
        )}
        {/* Debug info */}
        <div
          style={{
            position: 'fixed',
            top: 0,
            right: 0,
            background: 'red',
            color: 'white',
            padding: '4px',
            fontSize: '12px',
            zIndex: 9999,
          }}
        >
          NODE_ENV: {process.env.NODE_ENV || 'undefined'}
        </div>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};
