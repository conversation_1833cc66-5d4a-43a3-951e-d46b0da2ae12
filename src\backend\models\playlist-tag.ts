// src/backend/models/playlist-tag.ts

export interface PlaylistTag {
  id: string;
  name: string;
  color?: string;
  created_at: string;
  updated_at: string;
}

export interface CreatePlaylistTagData {
  name: string;
  color?: string;
}

export interface UpdatePlaylistTagData {
  name?: string;
  color?: string;
}

export interface PlaylistTagWithUsage extends PlaylistTag {
  usage_count: number;
  playlists: Array<{
    id: string;
    name: string;
  }>;
}

export interface PlaylistTagAssignment {
  id: number;
  playlist_id: string;
  tag_id: string;
  assigned_at: string;
}
