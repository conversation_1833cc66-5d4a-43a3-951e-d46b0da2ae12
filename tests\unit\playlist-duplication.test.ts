// tests/unit/playlist-duplication.test.ts

import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { Playlist } from '../../src/backend/models/playlist';
import { PlaylistRepository } from '../../src/backend/repositories/playlist-repository';
import { SongRepository } from '../../src/backend/repositories/song-repository';
import { PlaylistCrudService } from '../../src/backend/services/playlist-crud-service';
import {
  DatabaseError,
  PlaylistNotFoundError,
  ValidationError,
} from '../../src/shared/errors';

// Mock the repositories
jest.mock('../../src/backend/repositories/playlist-repository');
jest.mock('../../src/backend/repositories/song-repository');

describe('Playlist Duplication', () => {
  let playlistRepository: jest.Mocked<PlaylistRepository>;
  let songRepository: jest.Mocked<SongRepository>;
  let playlistService: PlaylistCrudService;

  beforeEach(() => {
    // Create mocked instances
    playlistRepository = new PlaylistRepository(
      null as any,
    ) as jest.Mocked<PlaylistRepository>;
    songRepository = new SongRepository(
      null as any,
    ) as jest.Mocked<SongRepository>;
    playlistService = new PlaylistCrudService(
      playlistRepository,
      songRepository,
    );

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('PlaylistCrudService.duplicatePlaylist', () => {
    const mockPlaylist: Playlist = {
      id: 'playlist-1',
      name: 'My Favorite Songs',
      description: 'A collection of my favorite tracks',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
    };

    const mockDuplicatedPlaylist: Playlist = {
      id: 'playlist-2',
      name: 'My Favorite Songs (Copy)',
      description: 'A collection of my favorite tracks',
      created_at: '2024-01-01T01:00:00.000Z',
      updated_at: '2024-01-01T01:00:00.000Z',
    };

    it('should duplicate a playlist with unique name generation', async () => {
      // Setup mocks
      playlistRepository.findById.mockResolvedValue(mockPlaylist);
      playlistRepository.findByName.mockResolvedValue(null); // No existing copy
      playlistRepository.duplicate.mockResolvedValue(mockDuplicatedPlaylist);

      // Execute
      const result = await playlistService.duplicatePlaylist('playlist-1');

      // Verify
      expect(result).toEqual(mockDuplicatedPlaylist);
      expect(playlistRepository.findById).toHaveBeenCalledWith('playlist-1');
      expect(playlistRepository.findByName).toHaveBeenCalledWith(
        'My Favorite Songs (Copy)',
      );
      expect(playlistRepository.duplicate).toHaveBeenCalledWith(
        'playlist-1',
        'My Favorite Songs (Copy)',
      );
    });

    it('should generate unique names for multiple duplicates', async () => {
      // Setup mocks
      playlistRepository.findById.mockResolvedValue(mockPlaylist);
      playlistRepository.findByName
        .mockResolvedValueOnce(mockDuplicatedPlaylist) // First copy exists
        .mockResolvedValueOnce(null); // Second copy doesn't exist
      playlistRepository.duplicate.mockResolvedValue({
        ...mockDuplicatedPlaylist,
        name: 'My Favorite Songs (Copy 2)',
      });

      // Execute
      const result = await playlistService.duplicatePlaylist('playlist-1');

      // Verify
      expect(result.name).toBe('My Favorite Songs (Copy 2)');
      expect(playlistRepository.findByName).toHaveBeenCalledWith(
        'My Favorite Songs (Copy)',
      );
      expect(playlistRepository.findByName).toHaveBeenCalledWith(
        'My Favorite Songs (Copy 2)',
      );
      expect(playlistRepository.duplicate).toHaveBeenCalledWith(
        'playlist-1',
        'My Favorite Songs (Copy 2)',
      );
    });

    it('should handle multiple existing copies correctly', async () => {
      // Setup mocks
      playlistRepository.findById.mockResolvedValue(mockPlaylist);
      playlistRepository.findByName
        .mockResolvedValueOnce(mockDuplicatedPlaylist) // Copy exists
        .mockResolvedValueOnce(mockDuplicatedPlaylist) // Copy 2 exists
        .mockResolvedValueOnce(mockDuplicatedPlaylist) // Copy 3 exists
        .mockResolvedValueOnce(null); // Copy 4 doesn't exist
      playlistRepository.duplicate.mockResolvedValue({
        ...mockDuplicatedPlaylist,
        name: 'My Favorite Songs (Copy 4)',
      });

      // Execute
      const result = await playlistService.duplicatePlaylist('playlist-1');

      // Verify
      expect(result.name).toBe('My Favorite Songs (Copy 4)');
      expect(playlistRepository.findByName).toHaveBeenCalledTimes(4);
      expect(playlistRepository.duplicate).toHaveBeenCalledWith(
        'playlist-1',
        'My Favorite Songs (Copy 4)',
      );
    });

    it('should throw PlaylistNotFoundError for non-existent playlist', async () => {
      // Setup mocks
      playlistRepository.findById.mockResolvedValue(null);

      // Execute & Verify
      await expect(
        playlistService.duplicatePlaylist('non-existent-id'),
      ).rejects.toThrow(PlaylistNotFoundError);

      expect(playlistRepository.findById).toHaveBeenCalledWith(
        'non-existent-id',
      );
      expect(playlistRepository.duplicate).not.toHaveBeenCalled();
    });

    it('should handle validation errors for invalid playlist ID', async () => {
      // Execute & Verify
      await expect(playlistService.duplicatePlaylist('')).rejects.toThrow(
        ValidationError,
      );

      await expect(
        playlistService.duplicatePlaylist(null as any),
      ).rejects.toThrow(ValidationError);

      await expect(
        playlistService.duplicatePlaylist(undefined as any),
      ).rejects.toThrow(ValidationError);

      expect(playlistRepository.findById).not.toHaveBeenCalled();
    });

    it('should handle database errors gracefully', async () => {
      // Setup mocks
      playlistRepository.findById.mockResolvedValue(mockPlaylist);
      playlistRepository.findByName.mockResolvedValue(null);
      playlistRepository.duplicate.mockRejectedValue(
        new Error('Database connection failed'),
      );

      // Execute & Verify
      await expect(
        playlistService.duplicatePlaylist('playlist-1'),
      ).rejects.toThrow(DatabaseError);

      expect(playlistRepository.duplicate).toHaveBeenCalled();
    });

    it('should handle edge cases in name generation', async () => {
      const playlistWithCopyInName: Playlist = {
        ...mockPlaylist,
        name: 'Original (Copy)',
      };

      // Setup mocks
      playlistRepository.findById.mockResolvedValue(playlistWithCopyInName);
      playlistRepository.findByName.mockResolvedValue(null);
      playlistRepository.duplicate.mockResolvedValue({
        ...mockDuplicatedPlaylist,
        name: 'Original (Copy) (Copy)',
      });

      // Execute
      const result = await playlistService.duplicatePlaylist('playlist-1');

      // Verify
      expect(result.name).toBe('Original (Copy) (Copy)');
      expect(playlistRepository.duplicate).toHaveBeenCalledWith(
        'playlist-1',
        'Original (Copy) (Copy)',
      );
    });

    it('should handle special characters in playlist names', async () => {
      const specialNamePlaylist: Playlist = {
        ...mockPlaylist,
        name: 'Playlist with "Quotes" & Symbols!',
      };

      // Setup mocks
      playlistRepository.findById.mockResolvedValue(specialNamePlaylist);
      playlistRepository.findByName.mockResolvedValue(null);
      playlistRepository.duplicate.mockResolvedValue({
        ...mockDuplicatedPlaylist,
        name: 'Playlist with "Quotes" & Symbols! (Copy)',
      });

      // Execute
      const result = await playlistService.duplicatePlaylist('playlist-1');

      // Verify
      expect(result.name).toBe('Playlist with "Quotes" & Symbols! (Copy)');
      expect(playlistRepository.duplicate).toHaveBeenCalledWith(
        'playlist-1',
        'Playlist with "Quotes" & Symbols! (Copy)',
      );
    });

    it('should prevent infinite loops in name generation', async () => {
      // Setup mocks - simulate always finding existing names
      playlistRepository.findById.mockResolvedValue(mockPlaylist);
      playlistRepository.findByName.mockResolvedValue(mockDuplicatedPlaylist); // Always return existing

      // Execute & Verify
      await expect(
        playlistService.duplicatePlaylist('playlist-1'),
      ).rejects.toThrow(DatabaseError);

      // Should have tried many times before giving up
      expect(playlistRepository.findByName).toHaveBeenCalledTimes(1000); // 1000 attempts
    });

    it('should handle very long playlist names', async () => {
      const longNamePlaylist: Playlist = {
        ...mockPlaylist,
        name: 'A'.repeat(90) + ' Very Long Name',
      };

      // Setup mocks
      playlistRepository.findById.mockResolvedValue(longNamePlaylist);
      playlistRepository.findByName.mockResolvedValue(null);
      playlistRepository.duplicate.mockResolvedValue({
        ...mockDuplicatedPlaylist,
        name: longNamePlaylist.name + ' (Copy)',
      });

      // Execute
      const result = await playlistService.duplicatePlaylist('playlist-1');

      // Verify
      expect(result.name).toBe(longNamePlaylist.name + ' (Copy)');
      expect(playlistRepository.duplicate).toHaveBeenCalledWith(
        'playlist-1',
        longNamePlaylist.name + ' (Copy)',
      );
    });
  });

  describe('Unique Name Generation Logic', () => {
    const mockPlaylist: Playlist = {
      id: 'playlist-1',
      name: 'Test Playlist',
      description: 'Test description',
      created_at: '2024-01-01T00:00:00.000Z',
      updated_at: '2024-01-01T00:00:00.000Z',
    };

    it('should generate sequential copy names', async () => {
      // Setup mocks for multiple calls
      playlistRepository.findById.mockResolvedValue(mockPlaylist);

      // First call - no existing copies
      playlistRepository.findByName.mockResolvedValueOnce(null);
      playlistRepository.duplicate.mockResolvedValueOnce({
        ...mockPlaylist,
        id: 'playlist-2',
        name: 'Test Playlist (Copy)',
      });

      // Second call - first copy exists
      playlistRepository.findByName
        .mockResolvedValueOnce({
          ...mockPlaylist,
          name: 'Test Playlist (Copy)',
        })
        .mockResolvedValueOnce(null);
      playlistRepository.duplicate.mockResolvedValueOnce({
        ...mockPlaylist,
        id: 'playlist-3',
        name: 'Test Playlist (Copy 2)',
      });

      // Execute first duplication
      const firstDuplicate =
        await playlistService.duplicatePlaylist('playlist-1');
      expect(firstDuplicate.name).toBe('Test Playlist (Copy)');

      // Execute second duplication
      const secondDuplicate =
        await playlistService.duplicatePlaylist('playlist-1');
      expect(secondDuplicate.name).toBe('Test Playlist (Copy 2)');
    });

    it('should handle gaps in copy numbering', async () => {
      // Setup mocks - Copy and Copy 2 exist, but Copy 3 doesn't
      playlistRepository.findById.mockResolvedValue(mockPlaylist);
      playlistRepository.findByName
        .mockResolvedValueOnce({
          ...mockPlaylist,
          name: 'Test Playlist (Copy)',
        })
        .mockResolvedValueOnce({
          ...mockPlaylist,
          name: 'Test Playlist (Copy 2)',
        })
        .mockResolvedValueOnce(null); // Copy 3 doesn't exist
      playlistRepository.duplicate.mockResolvedValue({
        ...mockPlaylist,
        id: 'playlist-4',
        name: 'Test Playlist (Copy 3)',
      });

      // Execute
      const result = await playlistService.duplicatePlaylist('playlist-1');

      // Verify
      expect(result.name).toBe('Test Playlist (Copy 3)');
      expect(playlistRepository.findByName).toHaveBeenCalledWith(
        'Test Playlist (Copy)',
      );
      expect(playlistRepository.findByName).toHaveBeenCalledWith(
        'Test Playlist (Copy 2)',
      );
      expect(playlistRepository.findByName).toHaveBeenCalledWith(
        'Test Playlist (Copy 3)',
      );
    });
  });

  describe('Error Handling', () => {
    it('should propagate PlaylistNotFoundError from repository', async () => {
      playlistRepository.findById.mockResolvedValue(null);

      await expect(
        playlistService.duplicatePlaylist('non-existent'),
      ).rejects.toThrow(PlaylistNotFoundError);
    });

    it('should wrap repository errors in DatabaseError', async () => {
      const mockPlaylist: Playlist = {
        id: 'playlist-1',
        name: 'Test Playlist',
        description: 'Test',
        created_at: '2024-01-01T00:00:00.000Z',
        updated_at: '2024-01-01T00:00:00.000Z',
      };

      playlistRepository.findById.mockResolvedValue(mockPlaylist);
      playlistRepository.findByName.mockResolvedValue(null);
      playlistRepository.duplicate.mockRejectedValue(
        new Error('Repository error'),
      );

      await expect(
        playlistService.duplicatePlaylist('playlist-1'),
      ).rejects.toThrow(DatabaseError);
    });

    it('should preserve existing DatabaseError from repository', async () => {
      const mockPlaylist: Playlist = {
        id: 'playlist-1',
        name: 'Test Playlist',
        description: 'Test',
        created_at: '2024-01-01T00:00:00.000Z',
        updated_at: '2024-01-01T00:00:00.000Z',
      };

      const originalError = new DatabaseError('Original database error');

      playlistRepository.findById.mockResolvedValue(mockPlaylist);
      playlistRepository.findByName.mockResolvedValue(null);
      playlistRepository.duplicate.mockRejectedValue(originalError);

      await expect(
        playlistService.duplicatePlaylist('playlist-1'),
      ).rejects.toThrow(originalError);
    });
  });
});
