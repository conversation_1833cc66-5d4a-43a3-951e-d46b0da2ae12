# Information Sources

This document outlines the primary and secondary sources of information that will be leveraged across all three research arcs.

## Primary Sources (Direct Technical Documentation & Code)

1.  **`better-sqlite3` Documentation:**
    *   Official API documentation for `better-sqlite3` on GitHub.
    *   Performance benchmarks and best practice guides from the library's authors.
    *   Source code of `better-sqlite3` for understanding its synchronous nature and performance characteristics.

2.  **`yt-dlp` Documentation:**
    *   Official `yt-dlp` documentation, particularly the sections on command-line options, format selection, and authentication.
    *   The `yt-dlp-wrap` library's documentation and source code.
    *   Community forums and issue trackers for `yt-dlp` to find solutions to common problems.

3.  **`fluent-ffmpeg` and `ffmpeg` Documentation:**
    *   Official `ffmpeg` documentation for command-line arguments related to format conversion, metadata embedding, and stream manipulation.
    *   The `fluent-ffmpeg` library's documentation and examples.

4.  **Electron and Node.js Documentation:**
    *   Official Electron documentation, especially sections on Inter-Process Communication (IPC), security (`contextBridge`, `preload` scripts), and application packaging.
    *   Node.js documentation for `fs` and `path` modules.
    *   `electron-store` library documentation for settings management.

5.  **Google API Documentation:**
    *   Google Identity Platform documentation for OAuth 2.0 flows for desktop applications.
    *   YouTube Data API v3 reference documentation, including quota information and usage policies.
    *   `googleapis` Node.js client library documentation.

## Secondary Sources (Articles, Tutorials, and Best Practices)

1.  **Technology Blogs and Publications:**
    *   Articles on web development and software architecture from sources like Smashing Magazine, InfoQ, and Martin Fowler's blog.
    *   Tutorials and guides on Electron application development from various online platforms.

2.  **Community Forums:**
    *   Stack Overflow, for specific technical questions and solutions related to the libraries and frameworks being used.
    *   Reddit communities such as r/electronjs, r/node, and r/sqlite.

3.  **Open Source Project Analysis:**
    *   Analysis of existing open-source YouTube downloaders or media managers to understand their architectural decisions and solutions to common problems (e.g., `youtube-dl-gui`, `4k-video-downloader` architecture if available).

4.  **Academic Papers and Journals:**
    *   (If necessary) Papers on database performance, file system organization, or secure application design from sources like ACM Digital Library or IEEE Xplore.