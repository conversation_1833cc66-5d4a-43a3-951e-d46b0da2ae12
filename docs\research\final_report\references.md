# Final Report: References

This document provides a consolidated list of all secondary sources cited during the research process.

## Arc 1: Local Media Library Management

*   **SQLite Sample Database And Its Diagram (in PDF format)**
    *   URL: https://www.sqlitetutorial.net/sqlite-sample-database/
*   **php - Database Design for Video-DB with Playlists - Stack Overflow**
    *   URL: https://stackoverflow.com/questions/10123267/database-design-for-video-db-with-playlists
*   **Database Design for Spotify.. | by <PERSON><PERSON><PERSON> | Towards Data Engineering | Medium**
    *   URL: https://medium.com/towards-data-engineering/design-the-database-for-a-system-like-spotify-95ffd1fb5927
*   **SQLite - Create a Relationship**
    *   URL: https://www.quackit.com/sqlite/tutorial/create_a_relationship.cfm
*   **How To Create Many-to-Many Relationships in SQL**
    *   URL: https://five.co/blog/how-to-create-many-to-many-relationships-in-sql/
*   **The SQLite Query Optimizer Overview**
    *   URL: https://www.sqlite.org/optoverview.html
*   **SQLite Optimizations for Ultra High-Performance**
    *   URL: https://www.powersync.com/blog/sqlite-optimizations-for-ultra-high-performance
*   **Squeezing Performance from SQLite: Indexes? Indexes! | by Jason Feinstein | Medium**
    *   URL: https://medium.com/@JasonWyatt/squeezing-performance-from-sqlite-indexes-indexes-c4e175f3c346
*   **Best practices for SQLite performance | App quality | Android Developers**
    *   URL: https://developer.android.com/topic/performance/sqlite-performance-best-practices
*   **performance - Why is this sqlite query much slower when I index the columns? - Database Administrators Stack Exchange**
    *   URL: https://dba.stackexchange.com/questions/150858/why-is-this-sqlite-query-much-slower-when-i-index-the-columns
*   **Stay organized with 6 file storage best practices | TechTarget**
    *   URL: https://www.techtarget.com/searchstorage/tip/Stay-organized-with-6-file-storage-best-practices
*   **How to organize files and folders | Zapier**
    *   URL: https://zapier.com/blog/organize-files-folders/
*   **Folder Structure Best Practices for Businesses**
    *   URL: https://www.suitefiles.com/guides/folder-structures-guide/
*   **php - Tips for managing a large number of files? - Stack Overflow**
    *   URL: https://stackoverflow.com/questions/671260/tips-for-managing-a-large-number-of-files
*   **r/datacurator on Reddit: Standard for metadata sidecar files like .metadata.json?**
    *   URL: https://www.reddit.com/r/datacurator/comments/nrcwce/standard_for_metadata_sidecar_files_like/
*   **performance - JSON file VS SQLite android - Stack Overflow**
    *   URL: https://stackoverflow.com/questions/8652005/json-file-vs-sqlite-android
*   **SQLite User Forum: Flat files vs SQLite**
    *   URL: https://sqlite.org/forum/forumpost/3d7be1ad3d?t=c
*   **r/DataHoarder on Reddit: Best practices around cross-platform, software-independent tags or metadata?**
    *   URL: https://www.reddit.com/r/DataHoarder/comments/9yul31/best_practices_around_crossplatform/
*   **Should I manage my data in-memory or use SQLite? | Qt Forum**
    *   URL: https://forum.qt.io/topic/98095/should-i-manage-my-data-in-memory-or-use-sqlite
*   **A Step-by-Step Guide to Integrating Better-SQLite3 with Electron JS App Using Create-React-App - DEV Community**
    *   URL: https://dev.to/arindam1997007/a-step-by-step-guide-to-integrating-better-sqlite3-with-electron-js-app-using-create-react-app-3k16
*   **Electron Database - Storage adapters for SQLite, Filesystem and In-Memory | RxDB - JavaScript Database**
    *   URL: https://rxdb.info/electron-database.html
*   **Running migration tool with electron interpreter in node mode · Issue #8341 · typeorm/typeorm**
    *   URL: https://github.com/typeorm/typeorm/issues/8341
*   **How to improve bulk insert speed in SQLite | PDQ**
    *   URL: https://www.pdq.com/blog/improving-bulk-insert-speed-in-sqlite-a-comparison-of-transactions/
*   **Squeezing Performance from SQLite: Insertions | by Jason Feinstein | Medium**
    *   URL: https://medium.com/@JasonWyatt/squeezing-performance-from-sqlite-insertions-971aff98eef2
*   **SQLite bulk INSERT benchmarking and optimization**
    *   URL: https://zerowidthjoiner.net/2021/02/21/sqlite-bulk-insert-benchmarking-and-optimization

## Arc 2: Robust Video Downloading and Conversion

*   **GitHub - yt-dlp/yt-dlp: A feature-rich command-line audio/video downloader**
    *   URL: https://github.com/yt-dlp/yt-dlp
*   **How to Use YT-DLP: Guide and Commands (2025)**
    *   URL: https://www.rapidseedbox.com/blog/yt-dlp-complete-guide
*   **Yt-dlp Commands: The Complete Tutorial For Beginners (2025) - OSTechNix**
    *   URL: https://ostechnix.com/yt-dlp-tutorial/
*   **r/DataHoarder on Reddit: TIL about yt-dlp's amazing --embed-metadata flag...**
    *   URL: https://www.reddit.com/r/DataHoarder/comments/13tnkn0/til_about_ytdlps_amazing_embedmetadata_flag_what/
*   **yt-dlp(1) — Arch manual pages**
    *   URL: https://man.archlinux.org/man/extra/yt-dlp/yt-dlp.1.en
*   **p-queue - npm**
    *   URL: https://www.npmjs.com/package/p-queue
*   **GitHub - sindresorhus/p-queue: Promise queue with concurrency control**
    *   URL: https://github.com/sindresorhus/p-queue
*   **Python Stacks, Queues, and Priority Queues in Practice – Real Python**
    *   URL: https://realpython.com/queue-in-python/
*   **What is Priority Queue | Introduction to Priority Queue - GeeksforGeeks**
    *   URL: https://www.geeksforgeeks.org/priority-queue-set-1-introduction/

## Arc 3: Secure API Integration and Settings

*   **safeStorage | Electron**
    *   URL: https://www.electronjs.org/docs/latest/api/safe-storage
*   **security - Best practices to store sensitive information in Electron desktop application - Stack Overflow**
    *   URL: https://stackoverflow.com/questions/63748729/best-practices-to-store-sensitive-information-in-electron-desktop-application
*   **How to securely store sensitive information in Electron with node-keytar | by Cameron Nokes | Medium**
    *   URL: https://medium.com/cameron-nokes/how-to-securely-store-sensitive-information-in-electron-with-node-keytar-51af99f1cfc4
*   **Build and Secure an Electron App - OpenID, OAuth, Node.js, and Express**
    *   URL: https://auth0.com/blog/securing-electron-applications-with-openid-connect-and-oauth-2/
*   **Context Isolation | Electron**
    *   URL: https://www.electronjs.org/docs/latest/tutorial/context-isolation
*   **contextBridge | Electron**
    *   URL: https://www.electronjs.org/docs/latest/api/context-bridge
*   **Using Preload Scripts | Electron**
    *   URL: https://www.electronjs.org/docs/latest/tutorial/tutorial-preload
*   **How to use preload.js properly in Electron - Stack Overflow**
    *   URL: https://stackoverflow.com/questions/57807459/how-to-use-preload-js-properly-in-electron