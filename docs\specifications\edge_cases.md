# Edge Cases & Error Conditions

This document outlines potential edge cases, error conditions, and boundary scenarios. It defines how the Playlistify application should behave in these situations to ensure robustness and a good user experience.

---

## 1. Playlist & Video Management

| ID         | Scenario                                                                    | Expected Behavior                                                                                                                                                                                            |
| :--------- | :-------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **EC-1.1** | User tries to import an invalid or non-existent YouTube URL.                | The `playlist:get-preview` IPC handler should return an error. The UI dialog will display a message "Invalid YouTube URL. Please check the link and try again."                                                 |
| **EC-1.2** | User tries to import a private or members-only YouTube playlist (without being authenticated). | The `yt-dlp` process will fail. The backend will return an error, and the UI will display a message "This playlist is private or unavailable."                                                              |
| **EC-1.3** | User creates a custom playlist with a title that already exists (case-insensitive). | The `playlist:create-custom` backend handler will perform a case-insensitive check and return a `DUPLICATE_TITLE` error. The UI will prevent creation and show the corresponding error message.                |
| **EC-1.4** | A playlist imported from YouTube contains deleted or private videos.        | The import process should not fail. It will import all available videos. The `HealthCheckService` will later mark the unavailable videos with the appropriate status ('DELETED', 'PRIVATE').                    |
| **EC-1.5** | User searches within a playlist using special characters or an empty query. | The search should handle special characters without crashing. An empty query should simply return the full, unfiltered list of videos.                                                                       |
| **EC-1.6** | A playlist or video title contains characters invalid for file systems.     | When downloading, the `sanitizeFilename` utility must be used to remove or replace invalid characters (e.g., `/`, `\`, `:`, `*`, `?`, `"`, `<`, `>`, `|`) before saving the file.                                  |

## 2. Downloading and File System

| ID         | Scenario                                                                    | Expected Behavior                                                                                                                                                                                            |
| :--------- | :-------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **EC-2.1** | Network connection is lost during a download.                               | The `yt-dlp` process will error out. The `DownloadManager` will catch the error and update the task's status to `FAILED` in the database, with an error message like "Network connection lost."                |
| **EC-2.2** | Insufficient disk space at the selected download location.                  | The `DownloadManager` must perform a disk space check before starting the download. If space is insufficient, the task is immediately marked as `FAILED` with an appropriate error message, and no download is attempted. |
| **EC-2.3** | The user manually deletes a downloaded video file from the file system.     | When the user tries to play the video, the `fs:verify-file-exists` check will fail. The player UI will display a "File not found" message with an option to re-download the video.                               |
| **EC-2.4** | The application lacks write permissions for the download directory.         | The download task will fail immediately. The task status will be set to `FAILED` with an error message indicating a permission error.                                                                        |
| **EC-2.5** | Two videos in a playlist result in the same sanitized filename.             | The download service should append a numeric suffix to subsequent conflicting filenames (e.g., `video.mp4`, `video (1).mp4`, `video (2).mp4`).                                                                  |
| **EC-2.6** | A selected video quality is no longer available when the download starts.   | The smart quality fallback mechanism (as per Story 3.2) must engage, automatically selecting the next-highest available quality for that video and proceeding with the download.                               |

## 3. Application State and Concurrency

| ID         | Scenario                                                                    | Expected Behavior                                                                                                                                                                                            |
| :--------- | :-------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **EC-3.1** | The application is closed or crashes while a download is in progress.       | Upon restart, the `BackgroundTaskService`'s `requeuePendingTasks` method will identify the unfinished task. It will be marked as `FAILED` because the underlying `yt-dlp` process was terminated. The user can then retry it. |
| **EC-3.2** | User attempts to delete a playlist while it is being downloaded.            | The application should prevent this action. The "Delete Playlist" option should be disabled, or if clicked, a modal should appear stating "Cannot delete a playlist while it is being downloaded."           |
| **EC-3.3** | User initiates a manual health check on a playlist that is already being checked by the automatic scheduler. | The `HealthCheckService` should be designed to be idempotent. It should detect that a check is already in progress for that playlist and either ignore the second request or wait for the first one to complete. |
| **EC-3.4** | User rapidly clicks a button that triggers an IPC call.                     | The UI should disable the button immediately after the first click and show a loading state to prevent multiple identical IPC calls from being sent.                                                            |

## 4. UI/UX

| ID         | Scenario                                                                    | Expected Behavior                                                                                                                                                                                            |
| :--------- | :-------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **EC-4.1** | A playlist or video title is extremely long.                                | The UI must handle long text gracefully using truncation with an ellipsis (`...`). A tooltip showing the full title should appear on hover.                                                                 |
| **EC-4.2** | A video thumbnail fails to load.                                            | The `CachedImage` component's fallback mechanism should engage, displaying a default placeholder icon (e.g., a play icon) so the UI layout does not break.                                                      |
| **EC-4.3** | The application window is resized to a very small dimension.                | The responsive design should adapt. The sidebar might collapse into an icon-only menu, and content should reflow without overlapping or becoming unusable.                                                     |