# Technology Stack & Build System

## Core Technologies

### Frontend

- **React 19** with TypeScript for UI components
- **TailwindCSS** for styling with YouTube-inspired design system
- **shadcn/ui** for consistent UI components
- **Lucide React** for icons
- **TanStack React Router** for navigation
- **TanStack React Query** for data fetching and caching
- **Zustand** for client-side state management
- **React Hook Form** with Zod validation

### Backend (Electron Main Process)

- **Node.js** with TypeScript
- **Electron** for desktop application framework
- **better-sqlite3** for local database
- **yt-dlp-wrap** for YouTube content extraction
- **electron-store** for settings persistence
- **winston** for logging
- **fs-extra** for enhanced file operations

### Build System

- **Electron Forge** with Webpack plugin
- **TypeScript** with strict type checking
- **Webpack** for bundling with separate configs for main/renderer processes
- **PostCSS** with Autoprefixer for CSS processing
- **ESLint** with TypeScript and React plugins
- **Prettier** for code formatting

## Project Architecture

### Directory Structure

```
src/
├── backend/          # Electron Main Process
│   ├── services/     # Core business logic
│   ├── ipc/          # IPC handlers
│   ├── utils/        # Utility functions
│   └── schema/       # Database schema
├── frontend/         # Electron Renderer Process
│   ├── components/   # React components
│   ├── pages/        # Page components
│   ├── stores/       # Zustand stores
│   ├── hooks/        # Custom React hooks
│   └── styles/       # CSS and styling
└── shared/           # Shared types and utilities
    ├── types/        # TypeScript interfaces
    └── errors.ts     # Error definitions
```

### Key Patterns

- **IPC Communication**: Type-safe communication between main and renderer processes
- **Service Layer**: Backend services handle business logic and external integrations
- **Repository Pattern**: Data access abstraction over SQLite
- **Error Handling**: Comprehensive error types with recovery strategies
- **State Management**: React Query for server state, Zustand for UI state

## Database Schema

### Core Tables

- `playlists`: Playlist metadata and settings
- `videos`: Video information and file paths
- `background_tasks`: Task queue and progress tracking

## Common Commands

### Development

```bash
npm run dev              # Start development server
npm run dev:debug        # Start with debug logging
npm run dev:clean        # Clean build and start development
```

### Building

```bash
npm run build           # Build for production
npm run build:watch     # Build with file watching
npm run package         # Package Electron app
npm run make            # Create distributables
```

### Code Quality

```bash
npm run lint            # Run ESLint with auto-fix
npm run format          # Format code with Prettier
npm run type-check      # Run TypeScript type checking
npm run validate        # Run all quality checks
```

### Testing

```bash
npm test                # Run unit tests
npm run test:coverage   # Run tests with coverage
npm run test:watch      # Run tests in watch mode
```

## Configuration Files

- `tsconfig.json`: TypeScript configuration with strict settings
- `tailwind.config.js`: TailwindCSS with YouTube-inspired theme
- `eslint.config.js`: ESLint rules for TypeScript and React
- `webpack.*.config.js`: Webpack configurations for different processes
- `package.json`: Dependencies and scripts

## External Dependencies

### Required Binaries

- **yt-dlp**: YouTube content extraction (auto-downloaded)
- **ffmpeg**: Video processing (auto-downloaded)

### Dependency Management

- Binaries are automatically downloaded and managed locally
- No system-wide installation required
- Platform-specific installation scripts handle setup

## Performance Considerations

- **Bundle Splitting**: Separate bundles for main and renderer processes
- **Code Splitting**: Lazy loading for non-critical components
- **Database Optimization**: Indexed queries and connection pooling
- **Memory Management**: Proper cleanup of resources and event listeners
- **Background Processing**: Non-blocking operations with progress tracking
