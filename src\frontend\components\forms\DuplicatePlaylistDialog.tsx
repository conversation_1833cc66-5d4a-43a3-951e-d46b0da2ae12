// src/frontend/components/forms/DuplicatePlaylistDialog.tsx

import { zodResolver } from '@hookform/resolvers/zod';
import { Copy, Loader2 } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { usePlaylistMutations } from '../../hooks/usePlaylistQueries';
import { Button } from '../ui/button';
import { Checkbox } from '../ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Separator } from '../ui/separator';

// Validation schema
const duplicatePlaylistSchema = z.object({
  newName: z
    .string()
    .min(1, 'Playlist name is required')
    .max(100, 'Playlist name must be less than 100 characters')
    .refine(
      name => !name.includes('<') && !name.includes('>'),
      'Playlist name contains invalid characters',
    ),
  includeSongs: z.boolean(),
  includeMetadata: z.boolean(),
});

type DuplicatePlaylistFormData = z.infer<typeof duplicatePlaylistSchema>;

export interface DuplicatePlaylistDialogProps {
  isOpen: boolean;
  onClose: () => void;
  sourcePlaylist: {
    id: string;
    name: string;
    description?: string;
    videoCount?: number;
    type: 'custom' | 'youtube';
  };
  onSuccess?: (duplicatedPlaylist: any) => void;
}

export const DuplicatePlaylistDialog: React.FC<
  DuplicatePlaylistDialogProps
> = ({ isOpen, onClose, sourcePlaylist, onSuccess }) => {
  const { duplicatePlaylist } = usePlaylistMutations();

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    watch,
    setValue,
  } = useForm<DuplicatePlaylistFormData>({
    resolver: zodResolver(duplicatePlaylistSchema) as any,
    defaultValues: {
      newName: `${sourcePlaylist.name} (Copy)`,
      includeSongs: true,
      includeMetadata: true,
    },
    mode: 'onChange',
  });

  const includeSongs = watch('includeSongs');
  const includeMetadata = watch('includeMetadata');

  const handleClose = () => {
    reset();
    onClose();
  };

  const onSubmit = async (data: DuplicatePlaylistFormData) => {
    try {
      const result = await duplicatePlaylist.mutateAsync({
        sourceId: sourcePlaylist.id,
        newName: data.newName,
        includeSongs: data.includeSongs,
        includeMetadata: data.includeMetadata,
      });

      onSuccess?.(result);
      handleClose();
    } catch (error) {
      // Error handling is done in the mutation hook
      console.error('Failed to duplicate playlist:', error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Copy className='h-5 w-5' />
            Duplicate Playlist
          </DialogTitle>
          <DialogDescription>
            Create a copy of "{sourcePlaylist.name}". You can customize the name
            and choose what to include.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
          {/* Source Playlist Info */}
          <div className='rounded-lg border bg-muted/50 p-4'>
            <div className='flex items-center justify-between'>
              <div>
                <h4 className='font-medium'>{sourcePlaylist.name}</h4>
                <p className='text-sm text-muted-foreground'>
                  {sourcePlaylist.type === 'youtube'
                    ? 'YouTube Playlist'
                    : 'Custom Playlist'}
                  {sourcePlaylist.videoCount !== undefined && (
                    <span> • {sourcePlaylist.videoCount} videos</span>
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* New Playlist Name */}
          <div className='space-y-2'>
            <Label htmlFor='newName'>New Playlist Name</Label>
            <Input
              id='newName'
              {...register('newName')}
              placeholder='Enter playlist name'
              className={errors.newName ? 'border-destructive' : ''}
            />
            {errors.newName && (
              <p className='text-sm text-destructive'>
                {errors.newName.message}
              </p>
            )}
          </div>

          <Separator />

          {/* Duplication Options */}
          <div className='space-y-4'>
            <h4 className='font-medium'>What to include in the copy:</h4>

            <div className='space-y-3'>
              <div className='flex items-center space-x-2'>
                <Checkbox
                  id='includeSongs'
                  checked={includeSongs}
                  onCheckedChange={checked =>
                    setValue('includeSongs', !!checked)
                  }
                />
                <Label htmlFor='includeSongs' className='text-sm font-normal'>
                  Include all songs/videos
                </Label>
              </div>

              <div className='flex items-center space-x-2'>
                <Checkbox
                  id='includeMetadata'
                  checked={includeMetadata}
                  onCheckedChange={checked =>
                    setValue('includeMetadata', !!checked)
                  }
                />
                <Label
                  htmlFor='includeMetadata'
                  className='text-sm font-normal'
                >
                  Include metadata (tags, privacy settings)
                </Label>
              </div>
            </div>

            {!includeSongs && (
              <div className='rounded-lg border border-amber-200 bg-amber-50 p-3'>
                <p className='text-sm text-amber-800'>
                  ⚠️ The duplicated playlist will be empty (no songs/videos will
                  be copied).
                </p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              type='button'
              variant='outline'
              onClick={handleClose}
              disabled={duplicatePlaylist.isPending}
            >
              Cancel
            </Button>
            <Button
              type='submit'
              disabled={!isValid || duplicatePlaylist.isPending}
            >
              {duplicatePlaylist.isPending ? (
                <>
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  Duplicating...
                </>
              ) : (
                <>
                  <Copy className='mr-2 h-4 w-4' />
                  Duplicate Playlist
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
