# Project Structure & Organization

## Root Directory Layout

```
playlistify-app/
├── .kiro/                    # Kiro IDE configuration
│   ├── settings/            # IDE settings and MCP config
│   ├── specs/               # Feature specifications
│   └── steering/            # AI assistant guidance (this file)
├── assets/                  # Static assets for app packaging
├── release/                 # Packaged application output (git-ignored)
├── src/                     # Main source code
├── tests/                   # Test suites and utilities
├── scripts/                 # Build and utility scripts
├── docs/                    # Project documentation
└── Console Logs/            # Development logging output
```

## Source Code Organization (`src/`)

### Backend Process (`src/backend/`)

```
backend/
├── main.ts                  # Electron main process entry point
├── preload.ts              # Secure preload script
├── database-manager.ts     # Database connection management
├── sqlite-adapter.ts       # SQLite database adapter
├── ipc/                    # IPC communication handlers
│   ├── app-handlers.ts     # Application-level IPC
│   ├── download-handlers.ts # Download operations
│   ├── file-handlers.ts    # File system operations
│   ├── playlist-handlers.ts # Playlist management
│   ├── settings-handlers.ts # Settings management
│   └── thumbnail-handlers.ts # Thumbnail operations
├── services/               # Core business logic
│   ├── application-lifecycle-service.ts
│   ├── error-handler-service.ts
│   ├── logger-service.ts
│   ├── playlist-manager.ts
│   ├── settings-service.ts
│   ├── thumbnail-service.ts
│   ├── window-manager-service.ts
│   ├── youtube-metadata-extraction-service.ts
│   └── ytDlpManager.ts
├── utils/                  # Backend utilities
│   ├── file-utils.ts       # File system operations
│   ├── logger.ts           # Logging utilities
│   └── path-utils.ts       # Path management
├── schema/                 # Database schema
│   └── database_schema.sql # SQLite table definitions
└── security/               # Security implementations
    ├── index.ts            # Security exports
    └── security-manager.ts # Security management
```

### Frontend Process (`src/frontend/`)

```
frontend/
├── App.tsx                 # Main React application
├── index.tsx              # Renderer process entry point
├── renderer-router.tsx    # Router configuration
├── components/            # React components
│   ├── common/            # Shared components
│   │   ├── ErrorBoundary.tsx
│   │   └── QueryStateHandler.tsx
│   ├── features/          # Feature-specific components
│   │   └── PlaylistActionsBar.tsx
│   ├── forms/             # Form components
│   │   └── SettingsValidation.tsx
│   └── layout/            # Layout components
│       └── Navigation.tsx
├── hooks/                 # Custom React hooks
├── lib/                   # Frontend utilities
│   └── router.ts          # Router utilities
├── pages/                 # Page components
│   ├── Dashboard.tsx      # Main dashboard
│   └── MyPlaylists.tsx    # Playlist management
├── stores/                # State management
│   ├── store-test-utils.ts # Testing utilities
│   └── store-utils.ts     # Store utilities
└── styles/                # Styling
    └── main.css           # Global styles
```

### Shared Code (`src/shared/`)

```
shared/
├── types/                 # TypeScript type definitions
├── constants/             # Application constants
└── errors.ts              # Error type definitions
```

## Test Organization (`tests/`)

```
tests/
├── __tests__/             # Unit tests
├── unit/                  # Service unit tests
├── integration/           # Integration tests
├── utils/                 # Test utilities
├── mocks/                 # Mock implementations
├── setup.ts               # Test configuration
└── README.md              # Testing documentation
```

## Configuration Files

### Build & Development

- `package.json` - Dependencies and scripts
- `tsconfig.json` - TypeScript configuration
- `webpack.*.config.js` - Webpack build configurations
- `electron-forge.config.js` - Electron packaging

### Code Quality

- `eslint.config.js` - Linting rules
- `.prettierrc` - Code formatting
- `jest.config.js` - Test configuration

### Styling

- `tailwind.config.js` - TailwindCSS configuration
- `postcss.config.js` - PostCSS processing

## File Naming Conventions

### Components

- **React Components**: PascalCase (e.g., `PlaylistCard.tsx`)
- **Pages**: PascalCase (e.g., `Dashboard.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `usePlaylistData.ts`)

### Services & Utilities

- **Services**: kebab-case with `-service` suffix (e.g., `playlist-manager.ts`)
- **Utilities**: kebab-case with descriptive names (e.g., `file-utils.ts`)
- **IPC Handlers**: kebab-case with `-handlers` suffix (e.g., `playlist-handlers.ts`)

### Types & Interfaces

- **Type Files**: kebab-case (e.g., `playlist-types.ts`)
- **Interfaces**: PascalCase with descriptive names
- **Enums**: PascalCase with descriptive names

## Import Path Aliases

Configured in `tsconfig.json` for clean imports:

```typescript
// Instead of: ../../../shared/types
import { PlaylistType } from '@/shared/types';

// Instead of: ../../components/ui/button
import { Button } from '@/components/ui/button';
```

## Key Architectural Patterns

### Separation of Concerns

- **Backend**: Business logic, data persistence, external integrations
- **Frontend**: UI components, user interactions, presentation logic
- **Shared**: Common types, utilities, and constants

### Communication Flow

1. **Frontend** → IPC → **Backend** for data operations
2. **Backend** → Database/File System for persistence
3. **Backend** → External APIs (YouTube) for content
4. **Backend** → IPC → **Frontend** for responses

### Error Handling

- Centralized error types in `src/shared/errors.ts`
- Service-level error handling with recovery strategies
- UI error boundaries for graceful degradation

### State Management

- **Server State**: TanStack React Query for API data
- **UI State**: Zustand stores for component state
- **Settings**: Electron Store for persistent configuration

## Development Workflow

### Adding New Features

1. Define types in `src/shared/types/`
2. Implement backend service in `src/backend/services/`
3. Create IPC handlers in `src/backend/ipc/`
4. Build frontend components in `src/frontend/components/`
5. Add tests in appropriate `tests/` subdirectory

### Database Changes

1. Update schema in `src/backend/schema/database_schema.sql`
2. Modify database adapter if needed
3. Update related services and types
4. Add migration logic if required
