<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>PlayListify</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      http-equiv="Content-Security-Policy"
      content="
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval';
      style-src 'self' 'unsafe-inline';
      img-src 'self' data: https://i.ytimg.com https://img.youtube.com https://yt3.ggpht.com https://i9.ytimg.com;
      font-src 'self' data:;
      connect-src 'self' https://www.googleapis.com https://youtube.googleapis.com;
      media-src 'self' data:;
    "
    />
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #1a1a1a;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
        z-index: 9999;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #333;
        border-top: 3px solid #007acc;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .loading-text {
        font-size: 16px;
        opacity: 0.8;
      }
    </style>
  </head>
  <body>
    <!-- Loading screen shown immediately -->
    <div id="loading-screen">
      <div class="loading-spinner"></div>
      <div class="loading-text">Loading Playlistify...</div>
    </div>

    <div id="root"></div>

    <script>
      // Fallback: Hide loading screen if React fails to load after 5 seconds
      window.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          const loadingScreen = document.getElementById('loading-screen');
          const rootElement = document.getElementById('root');

          // Only hide if React hasn't rendered anything
          if (loadingScreen && (!rootElement || !rootElement.hasChildNodes())) {
            console.warn(
              'React app failed to render, hiding loading screen as fallback',
            );
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.3s ease-out';
            setTimeout(() => {
              loadingScreen.style.display = 'none';
              // Show error message
              if (rootElement) {
                rootElement.innerHTML =
                  '<div style="padding: 20px; color: red; font-family: Arial;">Failed to load application. Please refresh the page.</div>';
              }
            }, 300);
          }
        }, 5000); // Wait 5 seconds before fallback
      });
    </script>
  </body>
</html>
