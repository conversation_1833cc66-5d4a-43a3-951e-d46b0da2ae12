# Dependencies
node_modules/
.pnp
.pnp.js

# Build outputs
.webpack/
dist/
build/
out/

# Coverage directory used by tools like istanbul
coverage/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Temporary folders
.tmp/
.temp/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Package files
*.tgz
*.tar.gz

# Test results
test-results/
playwright-report/

# Generated files
*.d.ts.map
*.js.map

# Lock files (don't format these)
package-lock.json
yarn.lock
pnpm-lock.yaml

# Config files that should maintain their formatting
.env*
.md*
*.config.js
forge.config.js