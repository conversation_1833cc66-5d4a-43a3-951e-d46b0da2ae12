import { <PERSON> } from '@tanstack/react-router';
import { Clock, Download, Music, Plus, TrendingUp } from 'lucide-react';
import React from 'react';
import { Button } from '../components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../components/ui/card';

export const Dashboard: React.FC = () => {
  console.log('🔍 DEBUGGING: Dashboard component rendering...');

  React.useEffect(() => {
    console.log('🔍 DEBUGGING: Dashboard component mounted successfully!');
  }, []);

  // Mock data for demonstration
  const stats = [
    {
      title: 'Total Playlists',
      value: '12',
      description: 'Active playlists',
      icon: Music,
      trend: '+2 this week',
    },
    {
      title: 'Total Videos',
      value: '248',
      description: 'Videos managed',
      icon: TrendingUp,
      trend: '+15 this week',
    },
    {
      title: 'Watch Time',
      value: '42h',
      description: 'Total duration',
      icon: Clock,
      trend: '+3h this week',
    },
    {
      title: 'Downloads',
      value: '156',
      description: 'Videos downloaded',
      icon: Download,
      trend: '+8 this week',
    },
  ];

  const recentPlaylists = [
    {
      id: 1,
      name: 'Coding Music',
      videos: 25,
      duration: '2h 15m',
      lastUpdated: '2 hours ago',
    },
    {
      id: 2,
      name: 'Workout Hits',
      videos: 18,
      duration: '1h 32m',
      lastUpdated: '1 day ago',
    },
    {
      id: 3,
      name: 'Study Focus',
      videos: 12,
      duration: '45m',
      lastUpdated: '3 days ago',
    },
  ];

  return (
    <div className='space-y-8'>
      {/* Welcome Section */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold'>Welcome back!</h1>
          <p className='mt-2 text-muted-foreground'>
            Here's what's happening with your playlists today.
          </p>
        </div>
        <Link to='/playlists'>
          <Button>
            <Plus className='mr-2 h-4 w-4' />
            Create Playlist
          </Button>
        </Link>
      </div>

      {/* Stats Grid */}
      <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
        {stats.map(stat => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title}>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>
                  {stat.title}
                </CardTitle>
                <Icon className='h-4 w-4 text-muted-foreground' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold'>{stat.value}</div>
                <p className='text-xs text-muted-foreground'>
                  {stat.description}
                </p>
                <p className='mt-1 text-xs text-green-600'>{stat.trend}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Recent Activity */}
      <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
        {/* Recent Playlists */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Playlists</CardTitle>
            <CardDescription>Your recently updated playlists</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {recentPlaylists.map(playlist => (
                <div
                  key={playlist.id}
                  className='flex items-center justify-between rounded-lg border p-3'
                >
                  <div className='flex items-center space-x-3'>
                    <div className='flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10'>
                      <Music className='h-5 w-5 text-primary' />
                    </div>
                    <div>
                      <p className='font-medium'>{playlist.name}</p>
                      <p className='text-sm text-muted-foreground'>
                        {playlist.videos} videos • {playlist.duration}
                      </p>
                    </div>
                  </div>
                  <div className='text-right'>
                    <p className='text-sm text-muted-foreground'>
                      {playlist.lastUpdated}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className='mt-4'>
              <Link to='/playlists'>
                <Button variant='outline' className='w-full'>
                  View All Playlists
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks and shortcuts</CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <Link to='/playlists'>
              <Button variant='outline' className='w-full justify-start'>
                <Plus className='mr-2 h-4 w-4' />
                Create New Playlist
              </Button>
            </Link>
            <Button variant='outline' className='w-full justify-start'>
              <Download className='mr-2 h-4 w-4' />
              Import from YouTube
            </Button>
            <Link to='/settings'>
              <Button variant='outline' className='w-full justify-start'>
                <Music className='mr-2 h-4 w-4' />
                Manage Downloads
              </Button>
            </Link>
            <Link to='/settings'>
              <Button variant='outline' className='w-full justify-start'>
                <TrendingUp className='mr-2 h-4 w-4' />
                View Analytics
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
