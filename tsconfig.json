﻿{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "outDir": "./dist",
    
    // Strict type checking options (enhanced for better type safety)
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": false,
    "noImplicitOverride": true,
    "exactOptionalPropertyTypes": false,
    "noPropertyAccessFromIndexSignature": false,
    "noImplicitThis": true,
    
    // Module resolution
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "moduleResolution": "node",
    "allowImportingTsExtensions": false,
    
    // Emit options
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": false,
    "importHelpers": true,
    "newLine": "lf",
    
    // Advanced options
    "skipLibCheck": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    
    // JSX
    "jsx": "react-jsx",
    
    // Path mapping (organized by domain)
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/services/*": ["src/services/*"],
      "@/utils/*": ["src/utils/*"],
      "@/types/*": ["src/types/*"],
      "@/shared/*": ["src/shared/*"],
      "@/handlers/*": ["src/handlers/*"],
      "@/repositories/*": ["src/repositories/*"],
      "@/adapters/*": ["src/adapters/*"],
      "@/styles/*": ["src/styles/*"],
      "@/lib/*": ["src/lib/*"],
      "@/frontend/*": ["src/frontend/*"],
      "@/backend/*": ["src/backend/*"]
    },
    
    "typeRoots": ["./types", "./node_modules/@types"],
    "types": ["node", "jest", "better-sqlite3"]
  },
  "include": [
    "src/**/*",
    "tests/**/*",
    "types/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "coverage",
    ".webpack",
    "**/*.spec.ts",
    "**/*.test.ts",
    "src/services/**/*",
    "src/handlers/**/*",
    "src/repositories/**/*",
    "src/backend/repositories/**/*",
    "src/backend/services/**/*",
    "src/frontend/components/features/**/*",
    "src/frontend/components/examples/**/*",
    "src/frontend/hooks/queries/**/*",
    "tests/__tests__/**/*"
  ],
  "ts-node": {
    "esm": false,
    "experimentalSpecifierResolution": "node"
  }
}
