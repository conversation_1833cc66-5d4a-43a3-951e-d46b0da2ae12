import { Download, FileText, Grid3X3, Music } from 'lucide-react';
import React from 'react';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { Checkbox } from '../ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Label } from '../ui/label';
import { Progress } from '../ui/progress';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Separator } from '../ui/separator';
import { useToast } from '../ui/use-toast';

// Types
export type ExportFormat = 'json' | 'csv' | 'm3u';

export interface ExportOptions {
  format: ExportFormat;
  includeMetadata: boolean;
  includeSongFiles: boolean;
  useRelativePaths: boolean;
  outputPath?: string;
  showSaveDialog: boolean;
}

export interface ExportProgress {
  isExporting: boolean;
  progress: number;
  currentStep: string;
  totalSteps: number;
  currentStepIndex: number;
}

export interface PlaylistExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedPlaylists: Array<{
    id: string;
    name: string;
    songCount: number;
  }>;
  onExport: (options: ExportOptions) => Promise<void>;
  exportProgress?: ExportProgress;
  className?: string;
}

const formatOptions: Array<{
  value: ExportFormat;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  fileExtension: string;
}> = [
  {
    value: 'json',
    label: 'JSON',
    description: 'Complete playlist data with full metadata',
    icon: FileText,
    fileExtension: '.json',
  },
  {
    value: 'csv',
    label: 'CSV',
    description: 'Spreadsheet-compatible format for analysis',
    icon: Grid3X3,
    fileExtension: '.csv',
  },
  {
    value: 'm3u',
    label: 'M3U Playlist',
    description: 'Media player compatible playlist format',
    icon: Music,
    fileExtension: '.m3u',
  },
];

export const PlaylistExportDialog: React.FC<PlaylistExportDialogProps> = ({
  open,
  onOpenChange,
  selectedPlaylists,
  onExport,
  exportProgress,
  className,
}) => {
  const { toast } = useToast();
  const [options, setOptions] = React.useState<ExportOptions>({
    format: 'json',
    includeMetadata: true,
    includeSongFiles: false,
    useRelativePaths: true,
    showSaveDialog: true,
  });

  const totalSongs = React.useMemo(
    () =>
      selectedPlaylists.reduce((sum, playlist) => sum + playlist.songCount, 0),
    [selectedPlaylists],
  );

  const selectedFormat = formatOptions.find(f => f.value === options.format);

  const handleFormatChange = (format: ExportFormat) => {
    setOptions(prev => ({ ...prev, format }));
  };

  const handleOptionChange = (key: keyof ExportOptions, value: boolean) => {
    setOptions(prev => ({ ...prev, [key]: value }));
  };

  const handleExport = async () => {
    try {
      await onExport(options);
      toast({
        title: 'Export Started',
        description: `Exporting ${selectedPlaylists.length} playlist(s) to ${options.format.toUpperCase()} format.`,
      });
    } catch (error) {
      toast({
        title: 'Export Failed',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };

  const isExporting = exportProgress?.isExporting || false;
  const canExport = selectedPlaylists.length > 0 && !isExporting;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn('max-w-2xl', className)}>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Download className='h-5 w-5' />
            Export Playlists
          </DialogTitle>
          <DialogDescription>
            Export {selectedPlaylists.length} playlist(s) with {totalSongs}{' '}
            total songs
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          {/* Selected Playlists Summary */}
          <div className='space-y-3'>
            <Label className='text-sm font-medium'>Selected Playlists</Label>
            <div className='max-h-32 space-y-2 overflow-y-auto rounded-md border p-3'>
              {selectedPlaylists.map(playlist => (
                <div
                  key={playlist.id}
                  className='flex items-center justify-between text-sm'
                >
                  <span className='font-medium'>{playlist.name}</span>
                  <span className='text-muted-foreground'>
                    {playlist.songCount} song
                    {playlist.songCount !== 1 ? 's' : ''}
                  </span>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Format Selection */}
          <div className='space-y-3'>
            <Label className='text-sm font-medium'>Export Format</Label>
            <RadioGroup
              value={options.format}
              onValueChange={handleFormatChange}
              className='space-y-3'
            >
              {formatOptions.map(format => {
                const Icon = format.icon;
                return (
                  <div
                    key={format.value}
                    className={cn(
                      'flex items-start space-x-3 rounded-lg border p-4 transition-colors',
                      options.format === format.value &&
                        'border-primary bg-primary/5',
                    )}
                  >
                    <RadioGroupItem
                      value={format.value}
                      id={format.value}
                      className='mt-1'
                    />
                    <div className='flex-1 space-y-1'>
                      <div className='flex items-center gap-2'>
                        <Icon className='h-4 w-4' />
                        <Label htmlFor={format.value} className='font-medium'>
                          {format.label} ({format.fileExtension})
                        </Label>
                      </div>
                      <p className='text-sm text-muted-foreground'>
                        {format.description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </RadioGroup>
          </div>

          <Separator />

          {/* Export Options */}
          <div className='space-y-4'>
            <Label className='text-sm font-medium'>Export Options</Label>

            <div className='space-y-3'>
              <div className='flex items-center space-x-2'>
                <Checkbox
                  id='includeMetadata'
                  checked={options.includeMetadata}
                  onCheckedChange={checked =>
                    handleOptionChange('includeMetadata', !!checked)
                  }
                />
                <Label htmlFor='includeMetadata' className='text-sm'>
                  Include detailed metadata
                </Label>
              </div>
              <p className='ml-6 text-xs text-muted-foreground'>
                Export creation dates, update timestamps, and additional
                playlist information
              </p>

              <div className='flex items-center space-x-2'>
                <Checkbox
                  id='includeSongFiles'
                  checked={options.includeSongFiles}
                  onCheckedChange={checked =>
                    handleOptionChange('includeSongFiles', !!checked)
                  }
                />
                <Label htmlFor='includeSongFiles' className='text-sm'>
                  Include file paths
                </Label>
              </div>
              <p className='ml-6 text-xs text-muted-foreground'>
                Export local file paths for songs (useful for M3U playlists)
              </p>

              {options.includeSongFiles && (
                <div className='ml-6 flex items-center space-x-2'>
                  <Checkbox
                    id='useRelativePaths'
                    checked={options.useRelativePaths}
                    onCheckedChange={checked =>
                      handleOptionChange('useRelativePaths', !!checked)
                    }
                  />
                  <Label htmlFor='useRelativePaths' className='text-sm'>
                    Use relative file paths
                  </Label>
                </div>
              )}

              <div className='flex items-center space-x-2'>
                <Checkbox
                  id='showSaveDialog'
                  checked={options.showSaveDialog}
                  onCheckedChange={checked =>
                    handleOptionChange('showSaveDialog', !!checked)
                  }
                />
                <Label htmlFor='showSaveDialog' className='text-sm'>
                  Choose save location
                </Label>
              </div>
              <p className='ml-6 text-xs text-muted-foreground'>
                Show file save dialog to choose export location
              </p>
            </div>
          </div>

          {/* Export Progress */}
          {isExporting && exportProgress && (
            <>
              <Separator />
              <div className='space-y-3'>
                <Label className='text-sm font-medium'>Export Progress</Label>
                <div className='space-y-2'>
                  <div className='flex items-center justify-between text-sm'>
                    <span>{exportProgress.currentStep}</span>
                    <span>
                      {exportProgress.currentStepIndex + 1} of{' '}
                      {exportProgress.totalSteps}
                    </span>
                  </div>
                  <Progress value={exportProgress.progress} className='h-2' />
                </div>
              </div>
            </>
          )}

          {/* Format-specific Information */}
          {selectedFormat && (
            <>
              <Separator />
              <div className='rounded-lg bg-muted/50 p-4'>
                <div className='mb-2 flex items-center gap-2'>
                  <selectedFormat.icon className='h-4 w-4' />
                  <span className='text-sm font-medium'>
                    {selectedFormat.label} Format Details
                  </span>
                </div>
                <div className='space-y-1 text-xs text-muted-foreground'>
                  {options.format === 'json' && (
                    <>
                      <p>• Complete playlist and song data in JSON format</p>
                      <p>
                        • Includes all metadata, timestamps, and relationships
                      </p>
                      <p>• Best for backup and data analysis purposes</p>
                    </>
                  )}
                  {options.format === 'csv' && (
                    <>
                      <p>• Spreadsheet-compatible tabular format</p>
                      <p>• One row per song with playlist information</p>
                      <p>• Ideal for data analysis and reporting</p>
                    </>
                  )}
                  {options.format === 'm3u' && (
                    <>
                      <p>• Standard playlist format for media players</p>
                      <p>• Compatible with VLC, Winamp, and other players</p>
                      <p>• Requires file paths to be included for playback</p>
                    </>
                  )}
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={isExporting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleExport}
            disabled={!canExport}
            className='min-w-[100px]'
          >
            {isExporting ? (
              <>
                <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent' />
                Exporting...
              </>
            ) : (
              <>
                <Download className='mr-2 h-4 w-4' />
                Export
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Hook for managing export functionality
export const usePlaylistExport = () => {
  const [exportProgress, setExportProgress] = React.useState<ExportProgress>({
    isExporting: false,
    progress: 0,
    currentStep: '',
    totalSteps: 0,
    currentStepIndex: 0,
  });

  const exportPlaylists = React.useCallback(
    async (playlistIds: string[], options: ExportOptions) => {
      setExportProgress({
        isExporting: true,
        progress: 0,
        currentStep: 'Preparing export...',
        totalSteps: 3,
        currentStepIndex: 0,
      });

      try {
        // Call the IPC handler for export
        const result = await window.electronAPI.playlist.exportMultiple({
          playlistIds,
          format: options.format,
          outputPath: options.outputPath,
          includeMetadata: options.includeMetadata,
          includeSongFiles: options.includeSongFiles,
          useRelativePaths: options.useRelativePaths,
          showSaveDialog: options.showSaveDialog,
        });

        setExportProgress(prev => ({
          ...prev,
          progress: 100,
          currentStep: 'Export completed',
          currentStepIndex: 2,
        }));

        // Reset after a delay
        setTimeout(() => {
          setExportProgress({
            isExporting: false,
            progress: 0,
            currentStep: '',
            totalSteps: 0,
            currentStepIndex: 0,
          });
        }, 2000);

        return result;
      } catch (error) {
        setExportProgress({
          isExporting: false,
          progress: 0,
          currentStep: '',
          totalSteps: 0,
          currentStepIndex: 0,
        });
        throw error;
      }
    },
    [],
  );

  const exportSinglePlaylist = React.useCallback(
    async (playlistId: string, options: ExportOptions) => {
      return exportPlaylists([playlistId], options);
    },
    [exportPlaylists],
  );

  return {
    exportProgress,
    exportPlaylists,
    exportSinglePlaylist,
  };
};
