{"name": "playlistify", "version": "1.0.0", "description": "A comprehensive playlist management application", "author": "Playlistify Team", "main": "./.webpack/main", "scripts": {"start": "npm run build:all && cross-env NODE_ENV=development electron main-simple.js", "start:forge": "cross-env NODE_ENV=development electron-forge start", "start:prod": "cross-env NODE_ENV=production electron-forge start", "start:simple": "npm run build:all && cross-env NODE_ENV=development electron main-simple.js", "dev": "npm run start", "dev:debug": "cross-env NODE_ENV=development DEBUG=* electron-forge start", "dev:clean": "npm run clean && npm run dev", "build": "cross-env NODE_ENV=production npm run build:main && npm run build:renderer", "build:main": "webpack --config webpack.main.config.js", "build:renderer": "webpack --config webpack.renderer.config.js", "build:preload": "webpack --config webpack.preload.config.js", "build:all": "npm run build:main && npm run build:renderer && npm run build:preload", "build:watch": "concurrently \"npm run build:main -- --watch\" \"npm run build:renderer -- --watch\"", "build:prod": "cross-env NODE_ENV=production npm run clean && npm run build:all", "clean": "rimraf .webpack dist coverage", "clean:all": "rimraf .webpack dist coverage node_modules/.cache", "clean:cache": "rimraf node_modules/.cache .webpack/cache", "lint": "eslint src tests --ext .ts,.tsx,.js,.jsx --fix", "lint:check": "eslint src tests --ext .ts,.tsx,.js,.jsx", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,css,md,json,yml,yaml}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,css,md,json,yml,yaml}\"", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "validate": "npm run type-check && npm run lint:check && npm run format:check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit tests/services tests/__tests__", "test:integration": "jest tests/integration", "test:edge-cases": "jest tests/edge_cases", "test:acceptance": "playwright test tests/acceptance", "test:all": "npm run test:unit && npm run test:integration && npm run test:acceptance", "test:ci": "npm run test:coverage && npm run test:acceptance", "package": "cross-env NODE_ENV=production electron-forge package", "make": "cross-env NODE_ENV=production electron-forge make", "make:all": "cross-env NODE_ENV=production electron-forge make --platform=win32,darwin,linux", "publish": "cross-env NODE_ENV=production electron-forge publish", "postinstall": "electron-rebuild", "prepare": "npm run build:all", "prepublishOnly": "npm run validate && npm run test:ci && npm run build:prod", "analyze": "cross-env NODE_ENV=production webpack --config webpack.analyzer.config.js", "analyze:bundle": "cross-env ANALYZE=true NODE_ENV=production webpack --config webpack.renderer.config.js", "perf:check": "node scripts/performance-check.js", "perf:build": "cross-env NODE_ENV=production npm run clean && npm run build:all && npm run perf:check", "perf:monitor": "cross-env NODE_ENV=development npm run dev", "deps:check": "npm outdated", "deps:update": "npm update", "security:audit": "npm audit", "security:fix": "npm audit fix", "security:scan": "npm audit --audit-level moderate", "security:report": "npm audit --json > security-audit.json", "security:check-deps": "npm audit --audit-level high --production"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron-forge/plugin-webpack": "^7.8.1", "@electron/fuses": "^1.8.0", "@fullhuman/postcss-purgecss": "^7.0.2", "@playwright/test": "^1.54.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/better-sqlite3": "^7.6.13", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/node": "^20.19.4", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vercel/webpack-asset-relocator-loader": "^1.10.0", "assert": "^2.1.0", "babel-jest": "^30.0.4", "babel-loader": "^10.0.0", "buffer": "^6.0.3", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.1", "css-loader": "^7.1.2", "cssnano": "^7.1.0", "electron": "^37.2.0", "electron-rebuild": "^3.2.9", "eslint": "^9.31.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "events": "^3.3.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "html-webpack-plugin": "^5.6.3", "http-server": "^14.1.1", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.5", "jest-transform-stub": "^2.0.0", "node-loader": "^2.1.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss-import": "^16.1.1", "prettier": "^3.6.2", "prettier-plugin-sort-imports": "^1.8.8", "prettier-plugin-tailwindcss": "^0.6.13", "process": "^0.11.10", "purgecss": "^7.0.2", "rimraf": "^6.0.1", "stream-browserify": "^3.0.0", "style-loader": "^4.0.0", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.0", "typescript": "^5.8.3", "url": "^0.11.4", "util": "^0.12.5", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@rse/ffmpeg": "^1.4.2", "@stagewise-plugins/react": "^0.6.2", "@stagewise/toolbar-react": "^0.6.2", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-router": "^1.127.9", "@types/fs-extra": "^11.0.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/sqlite3": "^3.1.11", "adm-zip": "^0.5.10", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "better-auth": "^1.2.12", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "electron-squirrel-startup": "^1.0.1", "electron-store": "^10.1.0", "electron-updater": "^6.6.2", "fs-extra": "^11.3.0", "googleapis": "^152.0.0", "immer": "^10.1.1", "init": "^0.1.2", "lucide-react": "^0.469.0", "p-queue": "^8.1.0", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "react": "^19.1.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-player": "^3.2.1", "shadcn": "^2.9.0", "sqlite3": "^5.1.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tar": "^7.4.3", "winston": "^3.17.0", "yt-dlp-wrap": "^2.3.12", "zod": "4.0.5", "zustand": "^5.0.6"}}