# API Documentation

This directory contains API documentation for the Playlistify application.

## Contents

- `ipc-api.md` - IPC communication API reference
- `backend-services.md` - Backend services documentation
- `frontend-components.md` - Frontend components API
- `data-models.md` - Data models and interfaces

## Guidelines

- Keep documentation up-to-date with code changes
- Include examples for complex APIs
- Document error conditions and edge cases
- Use TypeScript interfaces for type documentation