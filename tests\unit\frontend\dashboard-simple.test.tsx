/**
 * Simple Dashboard Rendering Test
 * 
 * This test focuses on the core issue: why Dashboard shows blank page
 */

import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import React from 'react';

// Mock TanStack Router to avoid Response object issues
jest.mock('@tanstack/react-router', () => ({
  Link: ({ children, to, ...props }: any) => <a href={to} {...props}>{children}</a>,
}));

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  Music: () => <div data-testid="music-icon">Music</div>,
  Plus: () => <div data-testid="plus-icon">Plus</div>,
  TrendingUp: () => <div data-testid="trending-icon">TrendingUp</div>,
  Clock: () => <div data-testid="clock-icon">Clock</div>,
  Download: () => <div data-testid="download-icon">Download</div>,
}));

// Mock CSS imports
jest.mock('../../../src/styles/globals.css', () => ({}));

describe('Dashboard Simple Rendering Test', () => {
  test('Dashboard component imports without errors', () => {
    expect(() => {
      require('../../../src/frontend/pages/Dashboard');
    }).not.toThrow();
  });

  test('UI Button component imports without errors', () => {
    expect(() => {
      require('../../../src/frontend/components/ui/button');
    }).not.toThrow();
  });

  test('UI Card component imports without errors', () => {
    expect(() => {
      require('../../../src/frontend/components/ui/card');
    }).not.toThrow();
  });

  test('Dashboard renders basic content', () => {
    const { Dashboard } = require('../../../src/frontend/pages/Dashboard');
    
    const { container } = render(<Dashboard />);
    
    // Check if container has any content
    expect(container.firstChild).toBeTruthy();
    
    // Check for key text content
    expect(container.textContent).toContain('Welcome back!');
  });

  test('UI components render independently', () => {
    const { Button } = require('../../../src/frontend/components/ui/button');
    
    render(<Button>Test Button</Button>);
    expect(screen.getByText('Test Button')).toBeInTheDocument();
  });

  test('Card components render independently', () => {
    const { Card, CardHeader, CardTitle, CardContent } = require('../../../src/frontend/components/ui/card');
    
    render(
      <Card>
        <CardHeader>
          <CardTitle>Test Card</CardTitle>
        </CardHeader>
        <CardContent>Test Content</CardContent>
      </Card>
    );
    
    expect(screen.getByText('Test Card')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  test('Dashboard renders all expected sections', () => {
    const { Dashboard } = require('../../../src/frontend/pages/Dashboard');
    
    render(<Dashboard />);
    
    // Check for main sections
    expect(screen.getByText('Welcome back!')).toBeInTheDocument();
    expect(screen.getByText('Total Playlists')).toBeInTheDocument();
    expect(screen.getByText('Recent Playlists')).toBeInTheDocument();
    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
  });

  test('Dashboard has proper CSS classes', () => {
    const { Dashboard } = require('../../../src/frontend/pages/Dashboard');
    
    const { container } = render(<Dashboard />);
    const dashboardElement = container.firstChild as HTMLElement;
    
    // Check if main container has expected classes
    expect(dashboardElement).toHaveClass('space-y-8');
  });

  test('No console errors during render', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    const { Dashboard } = require('../../../src/frontend/pages/Dashboard');
    render(<Dashboard />);
    
    expect(consoleSpy).not.toHaveBeenCalled();
    
    consoleSpy.mockRestore();
  });

  test('Dashboard content is visible (not hidden)', () => {
    const { Dashboard } = require('../../../src/frontend/pages/Dashboard');
    
    const { container } = render(<Dashboard />);
    
    // Check that content exists and is not empty
    expect(container.textContent?.trim()).toBeTruthy();
    expect(container.textContent?.length).toBeGreaterThan(0);
  });

  test('Check for import path issues', () => {
    // Test that all imports resolve correctly
    expect(() => {
      const { Dashboard } = require('../../../src/frontend/pages/Dashboard');
      const { Button } = require('../../../src/frontend/components/ui/button');
      const { Card } = require('../../../src/frontend/components/ui/card');
      const { cn } = require('../../../src/frontend/lib/utils');
    }).not.toThrow();
  });
});
