# Development Documentation

This directory contains documentation for developers working on Playlistify.

## Contents

- `setup.md` - Development environment setup
- `contributing.md` - Contribution guidelines
- `coding-standards.md` - Code style and standards
- `testing.md` - Testing guidelines and practices
- `debugging.md` - Debugging tips and techniques
- `build-process.md` - Build and deployment process

## Quick Start

1. Clone the repository
2. Install dependencies: `npm install`
3. Start development server: `npm run dev`
4. Run tests: `npm test`

See `setup.md` for detailed setup instructions.