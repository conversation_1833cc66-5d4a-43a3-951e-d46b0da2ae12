import { Outlet } from '@tanstack/react-router';
import React from 'react';
import { ErrorBoundary } from '../common/ErrorBoundary';
import { Navigation } from './Navigation';

export const RootLayout: React.FC = () => {
  console.log('🔍 DEBUGGING: RootLayout component rendering...');

  React.useEffect(() => {
    console.log('🔍 DEBUGGING: RootLayout component mounted successfully!');
  }, []);

  return (
    <div className='min-h-screen bg-background text-foreground'>
      <Navigation />
      <main className='container mx-auto px-4 py-8'>
        <ErrorBoundary>
          <Outlet />
        </ErrorBoundary>
      </main>
    </div>
  );
};
