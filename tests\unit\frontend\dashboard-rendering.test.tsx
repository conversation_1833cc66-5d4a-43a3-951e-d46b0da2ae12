/**
 * Dashboard Rendering Diagnostic Tests
 *
 * These tests are designed to diagnose why the Dashboard shows a blank page
 * instead of rendering properly. They test various aspects of the rendering pipeline.
 */

import { QueryClient } from '@tanstack/react-query';
import {
  createMemoryHistory,
  createRouter,
  RouterProvider,
} from '@tanstack/react-router';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import React from 'react';

// Import the actual components we're testing
import { App } from '../../../src/App';
import { Dashboard } from '../../../src/frontend/pages/Dashboard';

// Mock CSS imports
jest.mock('../../../src/styles/globals.css', () => ({}));

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  Music: () => <div data-testid='music-icon'>Music</div>,
  Plus: () => <div data-testid='plus-icon'>Plus</div>,
  TrendingUp: () => <div data-testid='trending-icon'>TrendingUp</div>,
  Clock: () => <div data-testid='clock-icon'>Clock</div>,
  Download: () => <div data-testid='download-icon'>Download</div>,
  Search: () => <div data-testid='search-icon'>Search</div>,
  Settings: () => <div data-testid='settings-icon'>Settings</div>,
  Home: () => <div data-testid='home-icon'>Home</div>,
  List: () => <div data-testid='list-icon'>List</div>,
}));

// Create a test query client
function createTestQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
}

describe('Dashboard Rendering Diagnostics', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = createTestQueryClient();
    // Clear any previous console errors
    jest.clearAllMocks();
  });

  afterEach(() => {
    queryClient.clear();
  });

  describe('1. Basic Component Rendering', () => {
    test('Dashboard component renders without crashing', () => {
      const { container } = render(<Dashboard />);
      expect(container).toBeInTheDocument();
    });

    test('Dashboard renders welcome message', () => {
      render(<Dashboard />);
      expect(screen.getByText('Welcome back!')).toBeInTheDocument();
    });

    test('Dashboard renders stats grid', () => {
      render(<Dashboard />);
      expect(screen.getByText('Total Playlists')).toBeInTheDocument();
      expect(screen.getByText('Total Videos')).toBeInTheDocument();
      expect(screen.getByText('Watch Time')).toBeInTheDocument();
      expect(screen.getByText('Downloads')).toBeInTheDocument();
    });

    test('Dashboard renders recent playlists section', () => {
      render(<Dashboard />);
      expect(screen.getByText('Recent Playlists')).toBeInTheDocument();
      expect(screen.getByText('Coding Music')).toBeInTheDocument();
      expect(screen.getByText('Workout Hits')).toBeInTheDocument();
      expect(screen.getByText('Study Focus')).toBeInTheDocument();
    });

    test('Dashboard renders quick actions section', () => {
      render(<Dashboard />);
      expect(screen.getByText('Quick Actions')).toBeInTheDocument();
      expect(screen.getByText('Create New Playlist')).toBeInTheDocument();
      expect(screen.getByText('Import from YouTube')).toBeInTheDocument();
    });
  });

  describe('2. UI Component Dependencies', () => {
    test('Button component works', () => {
      const { Button } = require('../../../src/frontend/components/ui/button');
      render(<Button>Test Button</Button>);
      expect(screen.getByText('Test Button')).toBeInTheDocument();
    });

    test('Card components work', () => {
      const {
        Card,
        CardHeader,
        CardTitle,
        CardContent,
      } = require('../../../src/frontend/components/ui/card');
      render(
        <Card>
          <CardHeader>
            <CardTitle>Test Card</CardTitle>
          </CardHeader>
          <CardContent>Test Content</CardContent>
        </Card>,
      );
      expect(screen.getByText('Test Card')).toBeInTheDocument();
      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });
  });

  describe('3. Router Integration', () => {
    test('Dashboard renders within router context', () => {
      // Create a simple router for testing
      const history = createMemoryHistory({ initialEntries: ['/'] });

      // Create a minimal route tree
      const rootRoute = {
        id: '__root__',
        path: '/',
        component: () => (
          <div data-testid='root-layout'>
            <Dashboard />
          </div>
        ),
      };

      const router = createRouter({
        routeTree: rootRoute as any,
        history,
      });

      render(<RouterProvider router={router} />);

      expect(screen.getByTestId('root-layout')).toBeInTheDocument();
      expect(screen.getByText('Welcome back!')).toBeInTheDocument();
    });
  });

  describe('4. Full App Integration', () => {
    test('App component renders without errors', async () => {
      // Mock the router module
      jest.doMock('../../../src/frontend/lib/router', () => ({
        router: {
          subscribe: jest.fn(),
          navigate: jest.fn(),
          state: {
            location: { pathname: '/' },
            matches: [],
          },
        },
      }));

      // Mock the query client
      jest.doMock('../../../src/frontend/lib/query-client', () => ({
        queryClient: createTestQueryClient(),
      }));

      // Mock error boundary and notification components
      jest.doMock(
        '../../../src/frontend/components/common/ErrorBoundary',
        () => ({
          ErrorBoundary: ({ children }: { children: React.ReactNode }) => (
            <div data-testid='error-boundary'>{children}</div>
          ),
        }),
      );

      jest.doMock(
        '../../../src/frontend/components/common/ErrorNotification',
        () => ({
          ErrorNotificationManager: () => (
            <div data-testid='error-notification-manager' />
          ),
        }),
      );

      const { container } = render(<App />);
      expect(container).toBeInTheDocument();
    });
  });

  describe('5. CSS and Styling', () => {
    test('Dashboard has proper CSS classes', () => {
      const { container } = render(<Dashboard />);
      const dashboardElement = container.firstChild as HTMLElement;

      // Check if Tailwind classes are applied
      expect(dashboardElement).toHaveClass('space-y-8');
    });

    test('Components have visible content (not hidden by CSS)', () => {
      render(<Dashboard />);

      const welcomeText = screen.getByText('Welcome back!');
      const computedStyle = window.getComputedStyle(welcomeText);

      // Check that the element is not hidden
      expect(computedStyle.display).not.toBe('none');
      expect(computedStyle.visibility).not.toBe('hidden');
      expect(computedStyle.opacity).not.toBe('0');
    });
  });

  describe('6. Error Detection', () => {
    test('No console errors during Dashboard render', () => {
      const consoleSpy = jest
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      render(<Dashboard />);

      expect(consoleSpy).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    test('No missing dependencies or import errors', () => {
      expect(() => {
        require('../../../src/frontend/pages/Dashboard');
      }).not.toThrow();

      expect(() => {
        require('../../../src/frontend/components/ui/button');
      }).not.toThrow();

      expect(() => {
        require('../../../src/frontend/components/ui/card');
      }).not.toThrow();
    });
  });

  describe('7. Content Visibility', () => {
    test('Dashboard content is actually visible in DOM', () => {
      const { container } = render(<Dashboard />);

      // Check that the container has content
      expect(container.textContent).toContain('Welcome back!');
      expect(container.textContent).toContain('Total Playlists');
      expect(container.textContent).toContain('Recent Playlists');
      expect(container.textContent).toContain('Quick Actions');

      // Check that content is not empty
      expect(container.textContent?.trim() || '').not.toBe('');
    });

    test('Dashboard has proper dimensions', () => {
      const { container } = render(<Dashboard />);
      const dashboardElement = container.firstChild as HTMLElement;

      // Element should exist and have content
      expect(dashboardElement).toBeTruthy();
      expect(dashboardElement.children.length).toBeGreaterThan(0);
    });
  });
});
