{"version": "0.2.0", "configurations": [{"name": "Debug Electron Main Process", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron", "windows": {"runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron.cmd"}, "args": [".webpack/main/index.js"], "outputCapture": "std", "console": "integratedTerminal", "env": {"NODE_ENV": "development"}, "preLaunchTask": "npm: build:main"}, {"name": "Debug Electron Renderer Process", "type": "chrome", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}/src", "timeout": 30000}, {"name": "Debug Jest Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-cache", "--no-coverage"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "test"}}, {"name": "Debug Current Jest Test", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-cache", "--no-coverage", "${relativeFile}"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "test"}}], "compounds": [{"name": "Debug Electron App", "configurations": ["Debug Electron Main Process", "Debug Electron Renderer Process"]}]}