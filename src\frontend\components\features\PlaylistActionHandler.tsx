// src/frontend/components/features/PlaylistActionHandler.tsx

import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { usePlaylistMutations } from '../../hooks/usePlaylistQueries';
import { DuplicatePlaylistDialog } from '../forms/DuplicatePlaylistDialog';
import { EditPlaylistDetailsDialog } from '../forms/EditPlaylistDetailsDialog';
import { ConfirmDeleteDialog } from '../ui/ConfirmDeleteDialog';
import type { Playlist } from './PlaylistGrid';

export interface PlaylistActionHandlerProps {
  children: (handlers: {
    handlePlaylistAction: (action: string, playlistId: string) => void;
    handleBulkAction: (action: string, playlistIds: string[]) => void;
  }) => React.ReactNode;
  playlists: Playlist[];
  onPlaylistSelect?: (id: string) => void;
  onPlaylistUpdated?: (playlist: Playlist) => void;
  onPlaylistDeleted?: (id: string) => void;
  onPlaylistDuplicated?: (newPlaylist: Playlist) => void;
}

export const PlaylistActionHandler: React.FC<PlaylistActionHandlerProps> = ({
  children,
  playlists,
  onPlaylistSelect,
  onPlaylistUpdated,
  onPlaylistDeleted,
  onPlaylistDuplicated,
}) => {
  const { updatePlaylist, deletePlaylist } = usePlaylistMutations();

  // Dialog states
  const [editDialog, setEditDialog] = useState<{
    isOpen: boolean;
    playlist?: Playlist;
  }>({ isOpen: false });

  const [duplicateDialog, setDuplicateDialog] = useState<{
    isOpen: boolean;
    playlist?: Playlist;
  }>({ isOpen: false });

  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    playlist?: Playlist;
    isMultiple?: boolean;
    playlistIds?: string[];
  }>({ isOpen: false });

  // Helper function to find playlist by ID
  const findPlaylist = (id: string): Playlist | undefined => {
    return playlists.find(p => p.id === id);
  };

  // Handle individual playlist actions
  const handlePlaylistAction = async (action: string, playlistId: string) => {
    const playlist = findPlaylist(playlistId);
    if (!playlist) {
      toast.error('Playlist not found');
      return;
    }

    switch (action) {
      case 'view':
      case 'select':
        onPlaylistSelect?.(playlistId);
        break;

      case 'play':
        // TODO: Implement playlist playback
        toast.success(`Playing "${playlist.title}"`);
        break;

      case 'edit':
        setEditDialog({ isOpen: true, playlist });
        break;

      case 'duplicate':
        setDuplicateDialog({ isOpen: true, playlist });
        break;

      case 'delete':
        setDeleteDialog({ isOpen: true, playlist });
        break;

      case 'toggle-privacy':
        try {
          const updatedPlaylist = await updatePlaylist.mutateAsync({
            id: playlistId,
            isPrivate: !playlist.isPrivate,
          });
          onPlaylistUpdated?.(updatedPlaylist);
          toast.success(
            `Playlist is now ${updatedPlaylist.isPrivate ? 'private' : 'public'}`,
          );
        } catch (_error) {
          // Error handling is done in the mutation hook
        }
        break;

      case 'toggle-favorite':
        // TODO: Implement favorites functionality
        toast.success(`Added "${playlist.title}" to favorites`);
        break;

      case 'share':
        // TODO: Implement sharing functionality
        if (navigator.share) {
          try {
            await navigator.share({
              title: playlist.title,
              text:
                playlist.description ||
                `Check out this playlist: ${playlist.title}`,
              url: playlist.source || window.location.href,
            });
          } catch (error) {
            // User cancelled sharing or sharing failed
            console.log('Sharing cancelled or failed:', error);
          }
        } else {
          // Fallback: copy to clipboard
          const shareText = `${playlist.title}${playlist.source ? ` - ${playlist.source}` : ''}`;
          try {
            await navigator.clipboard.writeText(shareText);
            toast.success('Playlist link copied to clipboard');
          } catch (_error) {
            toast.error('Failed to copy playlist link');
          }
        }
        break;

      case 'open-source':
        if (playlist.source) {
          window.open(playlist.source, '_blank');
        } else {
          toast.error('No source URL available');
        }
        break;

      case 'download':
        // TODO: Implement download functionality
        toast.success(`Downloading "${playlist.title}"`);
        break;

      case 'export':
        // TODO: Implement export functionality
        toast.success(`Exporting "${playlist.title}"`);
        break;

      default:
        console.warn(`Unknown playlist action: ${action}`);
        break;
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action: string, playlistIds: string[]) => {
    const selectedPlaylists = playlists.filter(p => playlistIds.includes(p.id));

    if (selectedPlaylists.length === 0) {
      toast.error('No playlists selected');
      return;
    }

    switch (action) {
      case 'delete-all':
        setDeleteDialog({
          isOpen: true,
          isMultiple: true,
          playlistIds,
        });
        break;

      case 'make-private':
        try {
          const promises = selectedPlaylists
            .filter(p => !p.isPrivate)
            .map(p =>
              updatePlaylist.mutateAsync({
                id: p.id,
                isPrivate: true,
              }),
            );

          await Promise.all(promises);
          toast.success(`Made ${promises.length} playlists private`);
        } catch (_error) {
          toast.error('Failed to update some playlists');
        }
        break;

      case 'make-public':
        try {
          const promises = selectedPlaylists
            .filter(p => p.isPrivate)
            .map(p =>
              updatePlaylist.mutateAsync({
                id: p.id,
                isPrivate: false,
              }),
            );

          await Promise.all(promises);
          toast.success(`Made ${promises.length} playlists public`);
        } catch (_error) {
          toast.error('Failed to update some playlists');
        }
        break;

      case 'add-to-favorites':
        // TODO: Implement bulk favorites
        toast.success(
          `Added ${selectedPlaylists.length} playlists to favorites`,
        );
        break;

      case 'download-all':
        // TODO: Implement bulk download
        toast.success(`Downloading ${selectedPlaylists.length} playlists`);
        break;

      case 'export-all':
        // TODO: Implement bulk export
        toast.success(`Exporting ${selectedPlaylists.length} playlists`);
        break;

      default:
        // Handle individual actions for each playlist
        for (const playlistId of playlistIds) {
          await handlePlaylistAction(action, playlistId);
        }
        break;
    }
  };

  // Handle edit dialog success
  const handleEditSuccess = (updatedPlaylist: Playlist) => {
    setEditDialog({ isOpen: false });
    onPlaylistUpdated?.(updatedPlaylist);
  };

  // Handle duplicate dialog success
  const handleDuplicateSuccess = (newPlaylist: Playlist) => {
    setDuplicateDialog({ isOpen: false });
    onPlaylistDuplicated?.(newPlaylist);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (deleteDialog.isMultiple && deleteDialog.playlistIds) {
      // Bulk delete
      try {
        const promises = deleteDialog.playlistIds.map(id =>
          deletePlaylist.mutateAsync(id),
        );
        await Promise.all(promises);

        deleteDialog.playlistIds.forEach(id => onPlaylistDeleted?.(id));
        toast.success(`Deleted ${deleteDialog.playlistIds.length} playlists`);
      } catch (_error) {
        toast.error('Failed to delete some playlists');
      }
    } else if (deleteDialog.playlist) {
      // Single delete
      try {
        await deletePlaylist.mutateAsync(deleteDialog.playlist.id);
        onPlaylistDeleted?.(deleteDialog.playlist.id);
      } catch (_error) {
        // Error handling is done in the mutation hook
      }
    }

    setDeleteDialog({ isOpen: false });
  };

  return (
    <>
      {children({ handlePlaylistAction, handleBulkAction })}

      {/* Edit Dialog */}
      {editDialog.playlist && (
        <EditPlaylistDetailsDialog
          isOpen={editDialog.isOpen}
          onClose={() => setEditDialog({ isOpen: false })}
          playlist={editDialog.playlist}
          onSuccess={handleEditSuccess}
        />
      )}

      {/* Duplicate Dialog */}
      {duplicateDialog.playlist && (
        <DuplicatePlaylistDialog
          isOpen={duplicateDialog.isOpen}
          onClose={() => setDuplicateDialog({ isOpen: false })}
          sourcePlaylist={{
            id: duplicateDialog.playlist.id,
            name: duplicateDialog.playlist.title,
            description: duplicateDialog.playlist.description,
            videoCount: duplicateDialog.playlist.videoCount,
            type: duplicateDialog.playlist.type,
          }}
          onSuccess={handleDuplicateSuccess}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDeleteDialog
        isOpen={deleteDialog.isOpen}
        onClose={() => setDeleteDialog({ isOpen: false })}
        onConfirm={handleDeleteConfirm}
        title={
          deleteDialog.isMultiple
            ? `Delete ${deleteDialog.playlistIds?.length} Playlists`
            : `Delete "${deleteDialog.playlist?.title}"`
        }
        description={
          deleteDialog.isMultiple
            ? `Are you sure you want to delete ${deleteDialog.playlistIds?.length} playlists? This action cannot be undone.`
            : `Are you sure you want to delete "${deleteDialog.playlist?.title}"? This action cannot be undone.`
        }
        confirmText='Delete'
        isDestructive
        isLoading={deletePlaylist.isPending}
      />
    </>
  );
};
