# Non-Functional Requirements

This document specifies the non-functional requirements (NFRs) for the Playlistify application. These requirements define the quality attributes and constraints of the system.

---

## 1.0 Performance

| ID      | Requirement                                                              | Measurement / Verification                                                                                                                                                                                                                                                        |
| :------ | :----------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **NFR-1.1** | **UI Responsiveness:** The user interface must remain responsive during background operations. | - User interactions (clicks, typing) must register and provide feedback in under 200ms, even while a playlist import or download is in progress. <br> - This will be verified via end-to-end tests using Playwright.                                                                      |
| **NFR-1.2** | **List Virtualization:** The application must efficiently render large lists of videos. | - When scrolling through a playlist of 1,000+ videos, the browser frame rate must remain at or above 50 FPS. <br> - The number of DOM nodes for list items should not exceed the number of visible items plus a small buffer (e.g., 10). Verified via Playwright and browser performance profiling. |
| **NFR-1.3** | **Search Performance:** Client-side search must be performant.            | - Filtering the video list based on a search query must complete within 50ms of the debounced input. <br> - The search input must be debounced by at least 300ms to avoid excessive re-renders. Verified via component-level performance tests.                                 |
| **NFR-1.4** | **Resource Usage:** The application should have low resource usage when idle. | - When idle (no active tasks or user interaction), the application's CPU usage should be less than 2%. <br> - Memory usage should be stable and not exhibit memory leaks over a 24-hour period. Verified via OS-level monitoring tools.                                                |
| **NFR-1.5** | **Startup Time:** The application should launch quickly.                  | - The main application window should be visible and interactive within 3 seconds of launching the application from a cold start. Verified via manual testing and automated launch scripts.                                                                                  |

## 2.0 Reliability & Data Integrity

| ID      | Requirement                                                              | Measurement / Verification                                                                                                                                                                                                                                                        |
| :------ | :----------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **NFR-2.1** | **Task Persistence:** Background tasks must be persistent across sessions. | - If the application is closed during an import or download, the task's state is saved. <br> - Upon restart, the `BackgroundTaskService` must identify and re-queue any unfinished tasks. This is verified by Test Case 4.1 in the Master Acceptance Test Plan.                 |
| **NFR-2.2** | **Transactional Integrity:** Database operations must be atomic.         | - All database operations that involve multiple related inserts or updates (e.g., creating a playlist and adding its videos) must be wrapped in a single transaction. <br> - Verification is done via code review and integration tests that simulate failures mid-operation. |
| **NFR-2.3** | **Error Handling:** The application must handle errors gracefully.       | - Backend errors (e.g., API failures, database errors) must be caught and logged, and must not crash the main process. <br> - The UI must display user-friendly error messages instead of technical error codes.                                                                   |
| **NFR-2.4** | **Data Validation:** All data must be validated before being processed.  | - All data received from external sources (e.g., YouTube API) or user input is validated against a defined schema (e.g., using Zod). <br> - Invalid data is rejected and an appropriate error is logged and communicated to the user.                                             |

## 3.0 Security

| ID      | Requirement                                                              | Measurement / Verification                                                                                                                                                                                                                                                        |
| :------ | :----------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **NFR-3.1** | **Secure IPC:** Communication between the main and renderer processes must be secure. | - The preload script must have `contextIsolation` enabled and `nodeIntegration` disabled. <br> - Only specific, required functionalities are exposed to the renderer process via the `contextBridge`. Verified by reviewing the Electron configuration and preload script.        |
| **NFR-3.2** | **File System Access:** The renderer process must not have direct file system access. | - All file system operations (read, write, delete) must be handled by the main process and exposed to the renderer process only through dedicated, secure IPC channels. Verified by code review and security audit.                                                                 |
| **NFR-3.3** | **Input Sanitization:** All user-provided input must be sanitized.      | - File paths, search queries, and playlist titles/descriptions are sanitized to prevent injection attacks or path traversal vulnerabilities. Verified by unit tests that attempt to inject malicious strings.                                                                    |
| **NFR-3.4** | **Credential Security:** OAuth credentials must not be exposed.          | - The application's `client_id` and `client_secret` for Google OAuth are obfuscated in production builds. <br> - User-specific tokens are stored encrypted on the user's machine using `electron-store` with a machine-specific key. Verified via build script and code review. |

## 4.0 Usability & Accessibility

| ID      | Requirement                                                              | Measurement / Verification                                                                                                                                                                                                                                                        |
| :------ | :----------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **NFR-4.1** | **Keyboard Navigation:** All interactive elements must be keyboard accessible. | - All buttons, inputs, and navigation links can be focused and activated using the Tab and Enter/Space keys. <br> - Focus order is logical and predictable. Verified via manual testing and automated accessibility checks.                                                           |
| **NFR-4.2** | **Accessibility Standards:** The application should meet basic accessibility standards. | - All images and icons have appropriate `alt` text or `aria-label` attributes. <br> - Color contrast ratios meet WCAG AA standards. <br> - The application is navigable and usable with a screen reader. Verified using accessibility audit tools like Lighthouse or Axe.        |
| **NFR-4.3** | **Destructive Action Confirmation:** Destructive actions must require user confirmation. | - Deleting a playlist or video requires the user to confirm the action in a modal dialog. <br> - The dialog clearly explains the consequence of the action. Verified via E2E tests for all deletion workflows.                                                                    |

## 5.0 Maintainability & Extensibility

| ID      | Requirement                                                              | Measurement / Verification                                                                                                                                                                                                                                                        |
| :------ | :----------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **NFR-5.1** | **Modularity:** The codebase must be modular and well-organized.         | - The code adheres to the layered architecture defined in `docs/architecture/high_level_design.md`. <br> - Services, repositories, and UI components are separated into distinct modules with clear responsibilities. Verified via code review.                               |
| **NFR-5.2** | **Code Quality:** The code must adhere to defined coding standards.      | - The entire TypeScript codebase passes linting and formatting checks (ESLint, Prettier) without errors. <br> - This is enforced automatically as a pre-commit hook.                                                                                                                |
| **NFR-5.3** | **Test Coverage:** Core business logic must have high unit test coverage. | - Core backend services (e.g., `PlaylistManager`, `DownloadManager`) and complex UI components must have a unit test coverage of at least 80%. Verified via code coverage reports from Jest.                                                                                    |