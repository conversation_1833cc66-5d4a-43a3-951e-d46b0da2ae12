# Code Analysis

Perform advanced code analysis with multiple inspection options.

## Analysis Menu:

### 1. Knowledge Graph Generation

- Map relationships between components
- Visualize dependencies
- Identify architectural patterns

### 2. Code Quality Evaluation

- Complexity metrics
- Maintainability index
- Technical debt assessment
- Code duplication detection

### 3. Performance Analysis

- Identify bottlenecks
- Memory usage patterns
- Algorithm complexity
- Database query optimization

### 4. Security Review

- Vulnerability scanning
- Input validation checks
- Authentication/authorization review
- Sensitive data handling

### 5. Architecture Review

- Design pattern adherence
- SOLID principles compliance
- Coupling and cohesion analysis
- Module boundaries

### 6. Test Coverage Analysis

- Coverage percentages
- Untested code paths
- Test quality assessment
- Missing edge cases

## Process:

1. Select analysis type based on need
2. Run appropriate tools and inspections
3. Generate comprehensive report
4. Provide actionable recommendations
5. Prioritize improvements by impact

## Output Format:

- Executive summary
- Detailed findings
- Risk assessment
- Improvement roadmap
- Code examples where relevant
