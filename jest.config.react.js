// jest.config.react.js - Configuration for React component testing

module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom', // Required for React component testing
  roots: ['<rootDir>/src', '<rootDir>/tests'],
  testMatch: [
    '**/dashboard-*.test.tsx',
    '**/*react*.test.tsx',
    '**/*component*.test.tsx',
    '**/*frontend*.test.tsx',
  ],
  transform: {
    '^.+\\.tsx?$': 'ts-jest',
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts', '@testing-library/jest-dom'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@shared/(.*)$': '<rootDir>/src/shared/$1',
    '^@backend/(.*)$': '<rootDir>/src/backend/$1',
    '^@frontend/(.*)$': '<rootDir>/src/frontend/$1',
    '^electron$': '<rootDir>/tests/mocks/electron.ts',
    // Mock CSS and asset imports
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$':
      'jest-transform-stub',
  },
  testTimeout: 15000,
  verbose: true,
  // React-specific configuration
  testEnvironmentOptions: {
    url: 'http://localhost',
  },
  // Transform ignore patterns for ES modules
  transformIgnorePatterns: [
    'node_modules/(?!(lucide-react|@tanstack|@testing-library)/)',
  ],
  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true,
  // Collect coverage from React components
  collectCoverageFrom: [
    'src/frontend/**/*.{ts,tsx}',
    '!src/frontend/**/*.d.ts',
    '!src/frontend/**/*.spec.ts',
    '!src/frontend/**/*.test.ts',
  ],
  coverageDirectory: 'coverage-react',
  coverageReporters: ['text', 'lcov', 'html'],
  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  // Global setup for React testing
  globals: {
    'ts-jest': {
      tsconfig: {
        jsx: 'react-jsx',
        esModuleInterop: true,
        allowSyntheticDefaultImports: true,
      },
    },
  },
};
