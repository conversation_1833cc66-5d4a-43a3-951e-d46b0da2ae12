{
  // TypeScript settings
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "typescript.updateImportsOnFileMove.enabled": "always",

  // ESLint settings
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.format.enable": true,
  "eslint.codeAction.showDocumentation": {
    "enable": true
  },

  // Prettier settings
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },

  // File associations
  "files.associations": {
    "*.css": "tailwindcss"
  },

  // Tailwind CSS settings
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],

  // Search settings
  "search.exclude": {
    "**/node_modules": true,
    "**/.webpack": true,
    "**/dist": true,
    "**/coverage": true,
    "**/.git": true,
    "**/test-results": true,
    "**/playwright-report": true
  },

  // File watcher settings
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/**": true,
    "**/.webpack/**": true,
    "**/dist/**": true,
    "**/coverage/**": true
  },

  // Emmet settings
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html"
  },

  // Git settings
  "git.ignoreLimitWarning": true,

  // Terminal settings
  "terminal.integrated.env.windows": {
    "NODE_ENV": "development"
  },

  // Debugging settings
  "debug.allowBreakpointsEverywhere": true,

  // Extension recommendations
  "extensions.recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-playwright.playwright",
    "ms-vscode.vscode-typescript-next"
  ]
}