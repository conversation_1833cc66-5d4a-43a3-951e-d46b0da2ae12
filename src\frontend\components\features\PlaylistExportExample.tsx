// src/frontend/components/features/PlaylistExportExample.tsx

import { Download } from 'lucide-react';
import React from 'react';
import { Button } from '../ui/button';
import { useToast } from '../ui/use-toast';
import {
  PlaylistExportDialog,
  usePlaylistExport,
} from './PlaylistExportDialog';

// Example component showing how to integrate playlist export functionality
export const PlaylistExportExample: React.FC = () => {
  const { toast } = useToast();
  const { exportPlaylists, exportProgress } = usePlaylistExport();
  const [showExportDialog, setShowExportDialog] = React.useState(false);

  // Mock selected playlists data
  const selectedPlaylists = [
    {
      id: 'playlist-1',
      name: 'My Favorite Songs',
      songCount: 25,
    },
    {
      id: 'playlist-2',
      name: 'Workout Mix',
      songCount: 18,
    },
  ];

  const handleExport = async (options: any) => {
    try {
      const playlistIds = selectedPlaylists.map(p => p.id);
      await exportPlaylists(playlistIds, options);

      toast({
        title: 'Export Completed',
        description: `Successfully exported ${selectedPlaylists.length} playlists in ${options.format.toUpperCase()} format.`,
      });

      setShowExportDialog(false);
    } catch (error) {
      toast({
        title: 'Export Failed',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className='space-y-4'>
      <div className='flex items-center justify-between'>
        <div>
          <h3 className='text-lg font-semibold'>Playlist Export Example</h3>
          <p className='text-sm text-muted-foreground'>
            Export {selectedPlaylists.length} selected playlists
          </p>
        </div>

        <Button
          onClick={() => setShowExportDialog(true)}
          className='gap-2'
          disabled={selectedPlaylists.length === 0}
        >
          <Download className='h-4 w-4' />
          Export Playlists
        </Button>
      </div>

      <div className='rounded-lg border p-4'>
        <h4 className='mb-2 font-medium'>Selected Playlists:</h4>
        <div className='space-y-2'>
          {selectedPlaylists.map(playlist => (
            <div
              key={playlist.id}
              className='flex items-center justify-between text-sm'
            >
              <span className='font-medium'>{playlist.name}</span>
              <span className='text-muted-foreground'>
                {playlist.songCount} songs
              </span>
            </div>
          ))}
        </div>
      </div>

      <PlaylistExportDialog
        open={showExportDialog}
        onOpenChange={setShowExportDialog}
        selectedPlaylists={selectedPlaylists}
        onExport={handleExport}
        exportProgress={exportProgress}
      />
    </div>
  );
};
