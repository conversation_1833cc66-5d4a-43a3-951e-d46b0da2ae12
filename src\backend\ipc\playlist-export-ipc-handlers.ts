// src/backend/ipc/playlist-export-ipc-handlers.ts

import { dialog, ipcMain, IpcMainInvokeEvent } from 'electron';
import { BaseError, ValidationError } from '../../shared/errors';
import { DatabaseService } from '../services';
import {
  ExportFormat,
  ExportResult,
  PlaylistExportService,
} from '../services/playlist-export-service';
import { StructuredLoggerService } from '../services/structured-logger-service';

// Import shared IPC types
import type { IPCRequest, IPCResponse } from './playlist-ipc-handlers';

// Export Operation Types
export interface ExportPlaylistsRequest {
  playlistIds: string[];
  format: ExportFormat;
  outputPath?: string;
  includeMetadata?: boolean;
  includeSongFiles?: boolean;
  useRelativePaths?: boolean;
  showSaveDialog?: boolean;
}

export interface ExportSinglePlaylistRequest {
  playlistId: string;
  format: ExportFormat;
  outputPath?: string;
  includeMetadata?: boolean;
  includeSongFiles?: boolean;
  useRelativePaths?: boolean;
  showSaveDialog?: boolean;
}

export interface GetSupportedFormatsRequest {}

export interface ValidateOutputPathRequest {
  outputPath: string;
  format: ExportFormat;
}

export interface GenerateFilenameRequest {
  format: ExportFormat;
  playlistCount: number;
  timestamp?: string;
}

export class PlaylistExportIPCHandlers {
  private logger: StructuredLoggerService;
  private exportService: PlaylistExportService;

  constructor(
    databaseService: DatabaseService,
    logger?: StructuredLoggerService,
  ) {
    this.logger = logger || new StructuredLoggerService();
    this.exportService = new PlaylistExportService(
      databaseService.getPlaylistRepository(),
      databaseService.getSongRepository(),
      this.logger,
    );
  }

  /**
   * Register all export-related IPC handlers
   */
  registerHandlers(): void {
    // Export operations
    ipcMain.handle(
      'playlist:export:multiple',
      this.handleExportPlaylists.bind(this),
    );
    ipcMain.handle(
      'playlist:export:single',
      this.handleExportSinglePlaylist.bind(this),
    );

    // Utility operations
    ipcMain.handle(
      'playlist:export:getSupportedFormats',
      this.handleGetSupportedFormats.bind(this),
    );
    ipcMain.handle(
      'playlist:export:validateOutputPath',
      this.handleValidateOutputPath.bind(this),
    );
    ipcMain.handle(
      'playlist:export:generateFilename',
      this.handleGenerateFilename.bind(this),
    );
    ipcMain.handle(
      'playlist:export:showSaveDialog',
      this.handleShowSaveDialog.bind(this),
    );

    this.logger.info('Playlist export IPC handlers registered');
  }

  /**
   * Unregister all export-related IPC handlers
   */
  unregisterHandlers(): void {
    const channels = [
      'playlist:export:multiple',
      'playlist:export:single',
      'playlist:export:getSupportedFormats',
      'playlist:export:validateOutputPath',
      'playlist:export:generateFilename',
      'playlist:export:showSaveDialog',
    ];

    channels.forEach(channel => {
      ipcMain.removeAllListeners(channel);
    });

    this.logger.info('Playlist export IPC handlers unregistered');
  }

  // Export Operation Handlers

  private async handleExportPlaylists(
    event: IpcMainInvokeEvent,
    request: IPCRequest<ExportPlaylistsRequest>,
  ): Promise<IPCResponse<ExportResult>> {
    return this.executeWithErrorHandling(
      'playlist:export:multiple',
      request,
      async () => {
        let outputPath = request.data.outputPath;

        // Show save dialog if no output path provided or explicitly requested
        if (!outputPath || request.data.showSaveDialog) {
          const dialogResult = await this.showSaveDialog(
            request.data.format,
            request.data.playlistIds.length,
          );

          if (dialogResult.canceled || !dialogResult.filePath) {
            throw new ValidationError('Export cancelled by user');
          }

          outputPath = dialogResult.filePath;
        }

        // Perform export
        const result = await this.exportService.exportPlaylists({
          playlistIds: request.data.playlistIds,
          format: request.data.format,
          outputPath,
          includeMetadata: request.data.includeMetadata,
          includeSongFiles: request.data.includeSongFiles,
          useRelativePaths: request.data.useRelativePaths,
        });

        // Emit progress events to frontend
        event.sender.send('playlist:export:completed', {
          result,
          requestId: request.requestId,
        });

        return result;
      },
    );
  }

  private async handleExportSinglePlaylist(
    event: IpcMainInvokeEvent,
    request: IPCRequest<ExportSinglePlaylistRequest>,
  ): Promise<IPCResponse<ExportResult>> {
    return this.executeWithErrorHandling(
      'playlist:export:single',
      request,
      async () => {
        let outputPath = request.data.outputPath;

        // Show save dialog if no output path provided or explicitly requested
        if (!outputPath || request.data.showSaveDialog) {
          const dialogResult = await this.showSaveDialog(
            request.data.format,
            1,
          );

          if (dialogResult.canceled || !dialogResult.filePath) {
            throw new ValidationError('Export cancelled by user');
          }

          outputPath = dialogResult.filePath;
        }

        // Perform export
        const result = await this.exportService.exportSinglePlaylist(
          request.data.playlistId,
          request.data.format,
          outputPath,
          {
            includeMetadata: request.data.includeMetadata,
            includeSongFiles: request.data.includeSongFiles,
            useRelativePaths: request.data.useRelativePaths,
          },
        );

        // Emit completion event to frontend
        event.sender.send('playlist:export:completed', {
          result,
          requestId: request.requestId,
        });

        return result;
      },
    );
  }

  // Utility Handlers

  private async handleGetSupportedFormats(
    event: IpcMainInvokeEvent,
    request: IPCRequest<GetSupportedFormatsRequest>,
  ): Promise<IPCResponse<ExportFormat[]>> {
    return this.executeWithErrorHandling(
      'playlist:export:getSupportedFormats',
      request,
      async () => {
        return this.exportService.getSupportedFormats();
      },
    );
  }

  private async handleValidateOutputPath(
    event: IpcMainInvokeEvent,
    request: IPCRequest<ValidateOutputPathRequest>,
  ): Promise<IPCResponse<any>> {
    return this.executeWithErrorHandling(
      'playlist:export:validateOutputPath',
      request,
      async () => {
        return await this.exportService.validateOutputPath(
          request.data.outputPath,
          request.data.format,
        );
      },
    );
  }

  private async handleGenerateFilename(
    event: IpcMainInvokeEvent,
    request: IPCRequest<GenerateFilenameRequest>,
  ): Promise<IPCResponse<string>> {
    return this.executeWithErrorHandling(
      'playlist:export:generateFilename',
      request,
      async () => {
        const timestamp = request.data.timestamp
          ? new Date(request.data.timestamp)
          : undefined;
        return this.exportService.generateDefaultFilename(
          request.data.format,
          request.data.playlistCount,
          timestamp,
        );
      },
    );
  }

  private async handleShowSaveDialog(
    event: IpcMainInvokeEvent,
    request: IPCRequest<{ format: ExportFormat; playlistCount: number }>,
  ): Promise<IPCResponse<Electron.SaveDialogReturnValue>> {
    return this.executeWithErrorHandling(
      'playlist:export:showSaveDialog',
      request,
      async () => {
        return await this.showSaveDialog(
          request.data.format,
          request.data.playlistCount,
        );
      },
    );
  }

  // Helper Methods

  /**
   * Show save dialog for export
   */
  private async showSaveDialog(
    format: ExportFormat,
    playlistCount: number,
  ): Promise<Electron.SaveDialogReturnValue> {
    const defaultFilename = this.exportService.generateDefaultFilename(
      format,
      playlistCount,
    );

    const filters: Electron.FileFilter[] = [];

    switch (format) {
      case 'json':
        filters.push({ name: 'JSON Files', extensions: ['json'] });
        break;
      case 'csv':
        filters.push({ name: 'CSV Files', extensions: ['csv'] });
        break;
      case 'm3u':
        filters.push({ name: 'M3U Playlist Files', extensions: ['m3u'] });
        break;
    }

    filters.push({ name: 'All Files', extensions: ['*'] });

    return await dialog.showSaveDialog({
      title: `Export ${playlistCount === 1 ? 'Playlist' : 'Playlists'}`,
      defaultPath: defaultFilename,
      filters,
      properties: ['createDirectory'],
    });
  }

  /**
   * Execute operation with comprehensive error handling
   */
  private async executeWithErrorHandling<T>(
    operation: string,
    request: IPCRequest,
    handler: () => Promise<T>,
  ): Promise<IPCResponse<T>> {
    const startTime = Date.now();

    try {
      // Log operation start
      this.logger.logUserAction(
        operation,
        request.userId || 'anonymous',
        request.sessionId || 'unknown',
        { requestId: request.requestId },
      );

      // Execute handler
      const result = await handler();

      // Log successful operation
      const duration = Date.now() - startTime;
      this.logger.info(`Export IPC operation completed: ${operation}`, {
        operation,
        duration,
        userId: request.userId,
        requestId: request.requestId,
      });

      return {
        success: true,
        data: result,
        requestId: request.requestId,
      };
    } catch (error) {
      const duration = Date.now() - startTime;

      // Log error
      this.logger.error(
        `Export IPC operation failed: ${operation}`,
        error instanceof Error ? error : new Error(String(error)),
        {
          operation,
          duration,
          userId: request.userId,
          requestId: request.requestId,
          requestData: this.sanitizeRequestData(request.data),
        },
      );

      // Format error for IPC response
      const formattedError = this.formatErrorForIPC(error);

      return {
        success: false,
        error: formattedError,
        requestId: request.requestId,
      };
    }
  }

  /**
   * Format error for IPC transmission
   */
  private formatErrorForIPC(error: any): {
    code: string;
    message: string;
    userMessage?: string;
    suggestions?: string[];
    recoverable?: boolean;
  } {
    if (error instanceof BaseError) {
      return {
        code: error.code,
        message: error.message,
        userMessage: error.userMessage,
        suggestions: error.suggestions,
        recoverable: error.recoverable,
      };
    }

    if (error instanceof Error) {
      return {
        code: 'EXPORT_ERROR',
        message: error.message,
        userMessage: 'Failed to export playlist. Please try again.',
        suggestions: [
          'Check that the output directory exists and is writable',
          'Ensure you have sufficient disk space',
          'Try a different output location',
        ],
        recoverable: true,
      };
    }

    return {
      code: 'UNKNOWN_EXPORT_ERROR',
      message: String(error),
      userMessage:
        'An unexpected error occurred during export. Please try again.',
      recoverable: true,
    };
  }

  /**
   * Sanitize request data for logging (remove sensitive information)
   */
  private sanitizeRequestData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized = { ...data };

    // Remove potentially sensitive fields
    const sensitiveFields = ['password', 'token', 'key', 'secret'];
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    return sanitized;
  }
}
