# Page snapshot

```yaml
- navigation "Main toolbar":
  - toolbar:
    - button "Select an element in the page to inspect it - Ctrl + Shift + C"
    - button "Toggle device toolbar - Ctrl + Shift + M"
  - tablist "Panels":
    - tab "Elements" [selected]
    - tab "Console"
    - tab "Sources"
    - tab "Network"
  - button "More tabs"
  - toolbar:
    - button "Open Console to view 1 error, 1 warning": 1 1
    - button "Settings - F1 - Shift + ?"
    - button "Customize and control DevTools":
      - button "Customize and control DevTools"
    - button "Close"
- tabpanel "Elements panel":
  - button "Switch to Accessibility Tree view"
  - main "DOM tree explorer":
    - tree "Page DOM":
      - treeitem "<!DOCTYPE html>"
      - treeitem "<html>" [expanded]
      - group:
        - treeitem "<head> Expand …</head>":
          - text: <head>
          - button "Expand"
          - text: …</head>
        - treeitem "<body>" [expanded] [selected]
        - group:
          - treeitem "<div id=\"app-container\"> Expand …</div> Enable grid mode":
            - text: <div id="app-container">
            - button "Expand"
            - text: …</div>
            - button "Enable grid mode": grid
          - 'treeitem "<div id=\"add-playlist-dialog\" class=\"modal\" style=\"display: none;\"> Expand …</div>"':
            - text: "<div id=\"add-playlist-dialog\" class=\"modal\" style=\"display: none;\">"
            - button "Expand"
            - text: …</div>
          - treeitem "<script src=\"./renderer.js\"></script>":
            - text: <script src="
            - link "./renderer.js"
            - text: "\"></script>"
          - treeitem "</body>"
        - treeitem "</html>"
  - navigation "DOM tree breadcrumbs":
    - list:
      - listitem:
        - link "html":
          - /url: "#"
      - listitem:
        - link "body":
          - /url: "#"
  - navigation "Side panel toolbar":
    - tablist:
      - tab "Styles" [selected]
      - tab "Computed"
      - tab "Layout"
      - tab "Event Listeners"
      - tab "DOM Breakpoints"
      - tab "Properties"
    - button "More tabs"
  - complementary "Side panel content":
    - tabpanel "Styles panel":
      - toolbar:
        - textbox "Filter"
        - button "Toggle Element State"
        - button "Element Classes"
        - button "New Style Rule"
        - button "Toggle common rendering emulations"
        - button "Show Computed Styles sidebar"
      - list:
        - listitem "element.style, css selector":
          - text: "element.style {"
          - tree
          - text: "}"
        - listitem "html, body, css selector":
          - link "main.css:1"
          - text: "html, body {"
          - tree:
            - 'treeitem "CSS property name: margin : CSS property value: 0;"': "margin : 0;"
            - 'treeitem "CSS property name: padding : CSS property value: 0;"': "padding : 0;"
            - 'treeitem "CSS property name: height : CSS property value: 100%;"': "height : 100%;"
            - 'treeitem "CSS property name: overflow : CSS property value: hidden;"': "overflow : hidden;"
            - 'treeitem "CSS property name: font-family : CSS property value: sans-serif;"': "font-family : sans-serif;"
          - text: "}"
        - listitem "body, css selector":
          - text: "user agent stylesheet body {"
          - tree:
            - 'treeitem "CSS property name: display : CSS property value: block;"': "display : block;"
            - 'treeitem "CSS property name: margin : CSS property value: 8px;"': "margin : 8px;"
          - text: "}"
      - text: margin ‒ ‒ border ‒ ‒ padding ‒ ‒ 229 × 535 ‒ ‒ ‒ ‒ ‒ ‒
- alert: DevTools is docked to right
```