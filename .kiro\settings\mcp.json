{"mcpServers": {"brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAYFx0NZJZPhcAP8V26A1Us3dDqnyR"}, "disabled": false, "autoApprove": ["brave_web_search"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "disabled": false, "autoApprove": ["resolve-library-id", "get-library-docs"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false, "autoApprove": ["sequentialthinking"]}, "clear-thought": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@waldzellai/clear-thought", "--key", "78f1a5f4-f6e1-47d1-9318-1fad3c39b965", "--profile", "private-dormouse-U2Xih0"], "disabled": false, "autoApprove": ["debuggingapproach"]}}}