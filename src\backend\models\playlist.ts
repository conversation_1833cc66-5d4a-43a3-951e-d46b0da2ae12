// src/backend/models/playlist.ts

export interface Playlist {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface CreatePlaylistData {
  name: string;
  description?: string;
}

export interface UpdatePlaylistData {
  name?: string;
  description?: string;
}

export interface PlaylistWithStats extends Playlist {
  video_count: number;
  total_duration: number;
}

export interface ExportedPlaylist {
  playlist: Playlist;
  songs: Array<{
    id: string;
    title: string;
    artist: string;
    album?: string;
    duration?: number;
    file_path?: string;
    position: number;
    added_at: string;
  }>;
  metadata: {
    exportedAt: string;
    totalSongs: number;
    totalDuration: number;
    format: 'json' | 'm3u' | 'csv';
  };
}
